{"name": "@repo/eslint-config", "version": "0.0.0", "type": "module", "private": true, "exports": {"./base": "./base.js", "./library": "./library.js", "./next-js": "./next.js", "./nest-js": "./nest.js", "./prettier-base": "./prettier-base.js", "./react-internal": "./react-internal.js"}, "devDependencies": {"@eslint/js": "^9.31.0", "@next/eslint-plugin-next": "^15.4.2", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-turbo": "^2.5.0", "globals": "^16.3.0", "prettier": "^3.2.5", "typescript": "^5.8.2", "typescript-eslint": "^8.37.0"}}