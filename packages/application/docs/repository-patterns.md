# Repository Patterns and Data Access

## Overview

The application layer uses the Repository pattern to abstract data access and provide a clean boundary between domain logic and persistence infrastructure. Repositories work with primitive types to maintain domain purity.

## Repository Interface Design

### Core Repository Contract
```typescript
interface SessionRepository {
  findById(sessionId: string): Promise<SessionPrimitives | null>;
  save(session: SessionPrimitives): Promise<void>;
}
```

### Key Characteristics
- **Primitive Types**: Repositories work with serialized primitives, not domain objects
- **Async Operations**: All operations return promises
- **Null Handling**: `findById` returns null for missing entities
- **Simple Interface**: Minimal surface area for easier testing and implementation

## Repository Usage Patterns

### 1. Loading Aggregates
```typescript
// Load from repository
const sessionDto = await this.sessionRepository.findById(sessionId);
if (!sessionDto) {
  throw new Error(`Session not found: ${sessionId}`);
}

// Reconstruct domain object
const session = Session.fromPrimitives(sessionDto);
```

### 2. Saving Aggregates
```typescript
// Execute domain operation
const updatedSession = session.startSession();

// Convert to primitives and save
await this.sessionRepository.save(updatedSession.toPrimitives());
```

### 3. Creating New Aggregates
```typescript
// Create new domain object
const session = Session.create(sessionId, config, adminId, mainRoomId);

// Save primitives
await this.sessionRepository.save(session.toPrimitives());
```

## Primitive Type Contracts

### SessionPrimitives Structure
```typescript
type SessionPrimitives = {
  sessionId: string;
  config: SessionConfigPrimitives;
  createdByUserId: string;
  hostId?: string;
  mainRoomId: string;
  currentRoundNo: number;
  rounds: RoundPrimitives[];
  participants: ParticipantPresencePrimitives[];
  rooms: RoomPrimitives[];
  createdAt: number;
  state: SessionState;
  version?: number;
};
```

### Nested Primitive Types
```typescript
type RoomPrimitives = {
  roomId: string;
  config: RoomConfigPrimitives;
  seats: SeatPrimitives[];
  createdAt: number;
};

type SeatPrimitives = {
  seatNo: number;
  participantId?: string;
  reservedSince?: number;
  assignedAt?: number;
  releasedAt?: number;
};
```

## Repository Implementation Strategies

### 1. Database Repository
```typescript
class DatabaseSessionRepository implements SessionRepository {
  constructor(private db: Database) {}

  async findById(sessionId: string): Promise<SessionPrimitives | null> {
    const row = await this.db.query(
      'SELECT * FROM sessions WHERE session_id = ?',
      [sessionId]
    );
    
    if (!row) return null;
    
    return this.mapRowToPrimitives(row);
  }

  async save(session: SessionPrimitives): Promise<void> {
    await this.db.transaction(async (tx) => {
      // Upsert session
      await tx.query(
        'INSERT OR REPLACE INTO sessions (...) VALUES (...)',
        this.mapPrimitivesToRow(session)
      );
      
      // Update related tables (rooms, participants, etc.)
      await this.saveRelatedEntities(tx, session);
    });
  }
}
```

### 2. Event Store Repository
```typescript
class EventStoreSessionRepository implements SessionRepository {
  constructor(private eventStore: EventStore) {}

  async findById(sessionId: string): Promise<SessionPrimitives | null> {
    // Load events for the aggregate
    const events = await this.eventStore.getEvents(sessionId);
    if (events.length === 0) return null;
    
    // Replay events to rebuild state
    return this.replayEvents(events);
  }

  async save(session: SessionPrimitives): Promise<void> {
    // This would typically save events, not the full state
    // For simplicity, we'll store snapshots
    await this.eventStore.saveSnapshot(session.sessionId, session);
  }
}
```

### 3. In-Memory Repository
```typescript
class InMemorySessionRepository implements SessionRepository {
  private sessions = new Map<string, SessionPrimitives>();

  async findById(sessionId: string): Promise<SessionPrimitives | null> {
    const session = this.sessions.get(sessionId);
    return session ? { ...session } : null; // Return copy
  }

  async save(session: SessionPrimitives): Promise<void> {
    this.sessions.set(session.sessionId, { ...session }); // Store copy
  }

  // Test helper methods
  clear(): void {
    this.sessions.clear();
  }

  count(): number {
    return this.sessions.size;
  }
}
```

## Data Mapping Strategies

### 1. Flat Table Mapping
```typescript
class FlatTableMapper {
  mapRowToPrimitives(row: DatabaseRow): SessionPrimitives {
    return {
      sessionId: row.session_id,
      config: JSON.parse(row.config_json),
      createdByUserId: row.created_by_user_id,
      hostId: row.host_id,
      mainRoomId: row.main_room_id,
      currentRoundNo: row.current_round_no,
      rounds: JSON.parse(row.rounds_json),
      participants: JSON.parse(row.participants_json),
      rooms: JSON.parse(row.rooms_json),
      createdAt: row.created_at,
      state: row.state,
      version: row.version,
    };
  }
}
```

### 2. Normalized Table Mapping
```typescript
class NormalizedTableMapper {
  async loadSessionWithRelations(sessionId: string): Promise<SessionPrimitives | null> {
    // Load main session record
    const session = await this.loadSession(sessionId);
    if (!session) return null;

    // Load related entities
    const [rounds, participants, rooms] = await Promise.all([
      this.loadRounds(sessionId),
      this.loadParticipants(sessionId),
      this.loadRooms(sessionId),
    ]);

    return {
      ...session,
      rounds,
      participants,
      rooms,
    };
  }
}
```

### 3. Document Store Mapping
```typescript
class DocumentStoreMapper {
  mapDocumentToPrimitives(doc: Document): SessionPrimitives {
    // Document stores naturally map to primitive structures
    return {
      sessionId: doc._id,
      ...doc.data,
    };
  }

  mapPrimitivesToDocument(session: SessionPrimitives): Document {
    const { sessionId, ...data } = session;
    return {
      _id: sessionId,
      data,
    };
  }
}
```

## Error Handling

### 1. Not Found Handling
```typescript
async findById(sessionId: string): Promise<SessionPrimitives | null> {
  try {
    const result = await this.db.query(/* ... */);
    return result ? this.mapToPrimitives(result) : null;
  } catch (error) {
    if (this.isNotFoundError(error)) {
      return null;
    }
    throw new RepositoryError('Failed to load session', { sessionId, error });
  }
}
```

### 2. Concurrency Handling
```typescript
async save(session: SessionPrimitives): Promise<void> {
  try {
    const result = await this.db.query(
      'UPDATE sessions SET ... WHERE session_id = ? AND version = ?',
      [...sessionData, session.sessionId, session.version]
    );
    
    if (result.affectedRows === 0) {
      throw new ConcurrencyError('Session was modified by another process');
    }
  } catch (error) {
    if (this.isConcurrencyError(error)) {
      throw new ConcurrencyError('Optimistic lock failure', { sessionId: session.sessionId });
    }
    throw new RepositoryError('Failed to save session', { sessionId: session.sessionId, error });
  }
}
```

## Testing Strategies

### 1. Repository Testing
```typescript
describe('SessionRepository', () => {
  let repository: SessionRepository;

  beforeEach(() => {
    repository = new InMemorySessionRepository();
  });

  it('should return null for non-existent session', async () => {
    const result = await repository.findById('non-existent');
    expect(result).toBeNull();
  });

  it('should save and retrieve session', async () => {
    const session = createTestSessionPrimitives();
    
    await repository.save(session);
    const retrieved = await repository.findById(session.sessionId);
    
    expect(retrieved).toEqual(session);
  });
});
```

### 2. Integration Testing
```typescript
describe('Database Integration', () => {
  let repository: DatabaseSessionRepository;
  let db: TestDatabase;

  beforeEach(async () => {
    db = await createTestDatabase();
    repository = new DatabaseSessionRepository(db);
  });

  afterEach(async () => {
    await db.cleanup();
  });

  it('should handle complex session with nested entities', async () => {
    const session = createComplexSessionPrimitives();
    
    await repository.save(session);
    const retrieved = await repository.findById(session.sessionId);
    
    expect(retrieved).toEqual(session);
    expect(retrieved.rooms).toHaveLength(session.rooms.length);
    expect(retrieved.participants).toHaveLength(session.participants.length);
  });
});
```

### 3. Mock Repository
```typescript
const mockRepository: jest.Mocked<SessionRepository> = {
  findById: jest.fn(),
  save: jest.fn(),
};

// Usage in command handler tests
mockRepository.findById.mockResolvedValue(testSessionPrimitives);
mockRepository.save.mockResolvedValue();
```

## Performance Considerations

### 1. Lazy Loading
Load only what's needed for the operation.

### 2. Caching
Implement caching for frequently accessed aggregates.

### 3. Connection Pooling
Use connection pools for database repositories.

### 4. Batch Operations
Consider batch operations for bulk updates.

## Design Principles

### 1. Domain Independence
Repositories don't leak persistence concerns into the domain.

### 2. Simple Interface
Minimal, focused interface for easier testing and implementation.

### 3. Primitive Boundaries
Work with serializable primitives, not domain objects.

### 4. Error Transparency
Clear error handling and propagation.

### 5. Testability
Easy to mock and test in isolation.

This repository pattern provides a clean abstraction for data access while maintaining domain purity and enabling flexible persistence strategies.
