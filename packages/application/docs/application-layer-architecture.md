# Spokenly Application Layer Architecture

## Overview

The application layer orchestrates domain operations by handling commands, coordinating with repositories, and publishing events. It serves as the boundary between external interfaces and the pure domain logic.

## Core Responsibilities

- **Command Processing**: Validate and execute domain commands
- **Event Publishing**: Emit domain events to external systems
- **Repository Coordination**: Load and save domain aggregates
- **Error Handling**: Convert domain errors to application responses
- **Transaction Management**: Ensure consistency across operations

## Architecture Patterns

### 1. Command Handler Pattern

The `SessionCommandHandler` processes all session-related commands:

```typescript
export class SessionCommandHandler {
  constructor(
    private readonly sessionRepository: SessionRepository,
    private readonly eventBus: EventBus,
  ) {}

  async handle(command: DomainCommand): Promise<SessionCommandResult> {
    // 1. Validate command structure
    // 2. Execute domain operation
    // 3. Save aggregate state
    // 4. Publish events
    // 5. Return result
  }
}
```

### 2. Repository Pattern

Abstract data access through interfaces:

```typescript
interface SessionRepository {
  findById(sessionId: string): Promise<SessionPrimitives | null>;
  save(session: SessionPrimitives): Promise<void>;
}
```

### 3. Event Bus Pattern

Decouple event publishing from command handling:

```typescript
interface EventBus {
  publishEvents(events: readonly DomainEvent[]): Promise<void>;
}
```

## Command Processing Flow

### 1. Command Validation
```typescript
// Structural validation
if (!isDomainCommand(command)) {
  throw new Error('Invalid command');
}

// Required field validation
if (!command.commandId) {
  throw new Error('Command ID not found in command');
}
```

### 2. Command Routing
Commands are routed by `kind` to specific handlers:

```typescript
switch (command.kind) {
  case 'CreateSession':
    return this.handleCreateSession(command);
  case 'StartSession':
  case 'PauseSession':
  case 'ResumeSession':
    return this.handleSessionLifecycle(command, sessionId);
  // ... other command types
}
```

### 3. Domain Operation Execution

#### For New Aggregates (Create)
```typescript
private async handleCreateSession(command: CreateSessionCommand) {
  // Convert primitives to value objects
  const sessionId = SessionId.fromPrimitives(command.payload.sessionId);
  const config = SessionConfig.fromPrimitives(command.payload.config);
  
  // Create new aggregate
  const session = Session.create(sessionId, config, adminId, mainRoomId);
  
  // Get events and save
  const domainEvents = session.getUncommittedEvents();
  await this.sessionRepository.save(session.toPrimitives());
  
  return { success: true, events: domainEvents, aggregateVersion: session.getVersion() };
}
```

#### For Existing Aggregates (Update)
```typescript
private async withSessionAggregate(
  command: DomainCommand,
  sessionId: string,
  operation: (session: Session) => Session,
): Promise<SessionCommandResult> {
  // Load aggregate
  const sessionDto = await this.sessionRepository.findById(sessionId);
  if (!sessionDto) {
    throw new Error(`Session not found: ${sessionId}`);
  }
  
  // Reconstruct domain object
  const session = Session.fromPrimitives(sessionDto);
  
  // Execute operation
  const updatedSession = operation(session);
  
  // Save and get events
  const domainEvents = updatedSession.getUncommittedEvents();
  await this.sessionRepository.save(updatedSession.toPrimitives());
  
  return { success: true, events: domainEvents, aggregateVersion: updatedSession.getVersion() };
}
```

### 4. Event Publishing
```typescript
// Publish events if successful
if (result.success) {
  await this.eventBus.publishEvents(result.events);
}
```

## Command Categories and Handlers

### Session Lifecycle
- **CreateSession**: Initialize new session with main room
- **StartSession**: Transition from SCHEDULED to RUNNING
- **PauseSession**: Suspend active session
- **ResumeSession**: Resume paused session
- **CompleteSession**: End session successfully
- **CancelSession**: Terminate session early

### Round Management
- **AddRound**: Add round to session agenda
- **StartRound**: Begin specific round
- **EndCurrentRound**: Complete active round

### Participant Management
- **AddParticipant**: Register participant in session
- **JoinParticipant**: Participant joins session
- **SoftLeaveParticipant**: Temporary departure
- **SeatParticipant**: Assign to room/seat

### Room Management
- **CreateBreakoutRoom**: Create new breakout room
- **AssignParticipantToRoom**: Move participant between rooms
- **ReleaseSeatByParticipant**: Free participant's seat
- **RotateBreakouts**: Shuffle room assignments

### Host Management
- **SetHost**: Assign session host role

## Result Types

### Success Result
```typescript
type SuccessfulCommandResult = {
  readonly success: true;
  readonly events: readonly DomainEvent[];
  readonly aggregateVersion: number;
};
```

### Failure Result
```typescript
type FailedCommandResult = {
  readonly success: false;
  readonly error: Error;
};
```

## Error Handling Strategy

### 1. Domain Error Propagation
Domain errors bubble up unchanged:
```typescript
try {
  const result = await this.executeCommand(command);
  return result;
} catch (error) {
  return {
    success: false,
    error: error instanceof Error ? error : new Error(String(error)),
  };
}
```

### 2. Validation Errors
Structural validation happens before domain operations:
- Invalid command structure
- Missing required fields
- Type validation failures

### 3. Business Logic Errors
Domain aggregates throw specific errors:
- `SessionStateError` - Invalid state transitions
- `SessionCapacityError` - Capacity violations
- `SeatStateError` - Invalid seat operations

## Integration Points

### 1. Repository Implementations
Concrete implementations handle persistence:
- Database storage
- Event store integration
- Caching strategies

### 2. Event Bus Implementations
Various event publishing strategies:
- In-memory event bus
- Message queue integration
- Event streaming platforms

### 3. External Services
Application layer coordinates with:
- Video conferencing providers
- Notification services
- Analytics systems

## Testing Strategy

### 1. Unit Tests
Test command handlers in isolation:
- Mock repositories and event bus
- Verify domain operations
- Assert event publishing

### 2. Integration Tests
Test with real implementations:
- Database integration
- Event publishing verification
- End-to-end command flows

## Design Principles

### 1. Thin Application Layer
Business logic stays in the domain layer.

### 2. Dependency Inversion
Depend on abstractions, not concretions.

### 3. Single Responsibility
Each handler focuses on one aggregate type.

### 4. Fail Fast
Validate early and provide clear error messages.

### 5. Idempotency Support
Commands include IDs for deduplication.

This application layer architecture provides a clean separation between domain logic and infrastructure concerns while maintaining strong typing and comprehensive error handling.
