# Spokenly Application Layer Documentation

## Overview

This directory contains comprehensive documentation for the Spokenly application layer, which orchestrates domain operations by handling commands, coordinating with repositories, and publishing events.

## Documentation Structure

### Core Architecture
- **[Application Layer Architecture](./application-layer-architecture.md)** - Overall architecture, patterns, and responsibilities
- **[Command Handling](./command-handling.md)** - Command processing patterns and validation strategies
- **[Event Processing](./event-processing.md)** - Event publishing and integration with external systems
- **[Repository Patterns](./repository-patterns.md)** - Data access patterns and persistence strategies

### Legacy Documentation
- **[Legacy Application Layer v0.0.0](./legacy-application-layer-v0.0.0.md)** - Previous documentation for reference (outdated)

## Quick Start

### Understanding the Application Layer

The application layer serves as the orchestration boundary between external interfaces and pure domain logic:

1. **Command Processing** - Validates and executes domain commands
2. **Event Publishing** - Emits domain events to external systems
3. **Repository Coordination** - Loads and saves domain aggregates
4. **Error Handling** - Converts domain errors to application responses
5. **Transaction Management** - Ensures consistency across operations

### Key Components

1. **SessionCommandHandler** - Processes all session-related commands
2. **EventBus** - Publishes domain events to subscribers
3. **SessionRepository** - Abstracts session persistence
4. **Command/Event Envelopes** - Provide correlation and metadata

### Architecture Patterns

```
Application Layer
├── Command Handlers     # Process domain commands
├── Event Bus           # Publish domain events
├── Repositories        # Abstract data access
├── Error Handling      # Convert domain errors
└── Integration Points  # External system coordination
```

## Core Responsibilities

### 1. Command Processing Flow
```
1. Validate command structure
2. Load aggregate from repository (if needed)
3. Execute domain operation
4. Save updated aggregate
5. Publish resulting events
6. Return success/failure result
```

### 2. Event Publishing
```
Domain Operation → Events → Event Bus → External Systems
                                    ├── Video Conferencing
                                    ├── Analytics
                                    ├── Notifications
                                    └── Monitoring
```

### 3. Repository Pattern
```
Application ←→ Repository Interface ←→ Concrete Implementation
                                    ├── Database Repository
                                    ├── Event Store Repository
                                    └── In-Memory Repository
```

## Usage Examples

### Command Handler Setup
```typescript
const commandHandler = new SessionCommandHandler(
  sessionRepository,
  eventBus
);

const result = await commandHandler.handle(command);
if (result.success) {
  console.log('Command processed successfully');
  console.log('Events published:', result.events.length);
} else {
  console.error('Command failed:', result.error);
}
```

### Repository Usage
```typescript
// Load aggregate
const sessionDto = await sessionRepository.findById(sessionId);
const session = Session.fromPrimitives(sessionDto);

// Execute domain operation
const updatedSession = session.startSession();

// Save changes
await sessionRepository.save(updatedSession.toPrimitives());
```

### Event Bus Integration
```typescript
class VideoConferencingEventHandler {
  async handle(event: DomainEvent): Promise<void> {
    switch (event.kind) {
      case 'BreakoutRoomCreated':
        await this.createMeetingRoom(event.payload);
        break;
      case 'ParticipantAssignedToRoom':
        await this.assignParticipantToMeeting(event.payload);
        break;
    }
  }
}
```

## Command Categories

### Session Lifecycle
- **CreateSession** - Initialize new session with main room
- **StartSession** - Transition from SCHEDULED to RUNNING
- **PauseSession** - Suspend active session
- **CompleteSession** - End session successfully

### Participant Management
- **AddParticipant** - Register participant in session
- **JoinParticipant** - Participant joins session
- **SeatParticipant** - Assign to room/seat
- **SoftLeaveParticipant** - Temporary departure

### Room Management
- **CreateBreakoutRoom** - Create new breakout room
- **AssignParticipantToRoom** - Move participant between rooms
- **RotateBreakouts** - Shuffle room assignments

### Round Management
- **AddRound** - Add round to session agenda
- **StartRound** - Begin specific round
- **EndCurrentRound** - Complete active round

## Error Handling Strategy

### 1. Validation Errors
- Invalid command structure
- Missing required fields
- Type validation failures

### 2. Domain Errors
- Business rule violations
- Invalid state transitions
- Capacity constraints

### 3. Infrastructure Errors
- Repository failures
- Event bus failures
- Network timeouts

### 4. Error Propagation
```typescript
try {
  const result = await this.executeCommand(command);
  return result;
} catch (error) {
  return {
    success: false,
    error: error instanceof Error ? error : new Error(String(error)),
  };
}
```

## Integration Points

### 1. External Services
- **Video Conferencing** - Meeting room management
- **Analytics** - Session metrics and tracking
- **Notifications** - User alerts and updates
- **Monitoring** - System health and performance

### 2. Event-Driven Architecture
```
Domain Events → Event Bus → Multiple Subscribers
                         ├── Video Provider Adapter
                         ├── Analytics Service
                         ├── Notification Service
                         └── Audit Logger
```

### 3. Repository Implementations
- **Database Repository** - SQL/NoSQL persistence
- **Event Store Repository** - Event sourcing support
- **Cache Repository** - Performance optimization
- **In-Memory Repository** - Testing and development

## Testing Strategies

### 1. Unit Testing
```typescript
describe('SessionCommandHandler', () => {
  let handler: SessionCommandHandler;
  let mockRepository: jest.Mocked<SessionRepository>;
  let mockEventBus: jest.Mocked<EventBus>;

  beforeEach(() => {
    mockRepository = { findById: jest.fn(), save: jest.fn() };
    mockEventBus = { publishEvents: jest.fn() };
    handler = new SessionCommandHandler(mockRepository, mockEventBus);
  });

  it('should process valid commands', async () => {
    // Test implementation
  });
});
```

### 2. Integration Testing
- Test with real repository implementations
- Verify event publishing behavior
- Test error handling scenarios

### 3. End-to-End Testing
- Full command processing flow
- External system integration
- Performance and reliability testing

## Design Principles

### 1. Thin Application Layer
Business logic stays in the domain layer; application layer only orchestrates.

### 2. Dependency Inversion
Depend on abstractions (interfaces) not concrete implementations.

### 3. Single Responsibility
Each component has a focused, well-defined purpose.

### 4. Fail Fast
Validate early and provide clear error messages.

### 5. Idempotency Support
Commands include IDs for deduplication and retry safety.

## Performance Considerations

### 1. Async Operations
All operations are asynchronous to avoid blocking.

### 2. Batch Processing
Events are published in batches for efficiency.

### 3. Connection Pooling
Repository implementations should use connection pools.

### 4. Caching Strategies
Consider caching for frequently accessed aggregates.

## Contributing

When modifying the application layer:

1. **Maintain Separation** - Keep business logic in the domain layer
2. **Follow Patterns** - Use established command handling patterns
3. **Handle Errors** - Provide comprehensive error handling
4. **Test Thoroughly** - Cover both success and failure scenarios
5. **Update Documentation** - Keep documentation current with changes

## Related Documentation

- [Domain Layer Documentation](../../domain/docs/README.md) - Pure business logic and domain model
- [Infrastructure Documentation](../../infrastructure/docs/README.md) - Concrete implementations and external integrations (if available)

This application layer provides a clean orchestration boundary that maintains domain purity while enabling robust integration with external systems and infrastructure concerns.
