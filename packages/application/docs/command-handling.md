# Command Handling Patterns

## Overview

The application layer implements a robust command handling system that processes domain commands, coordinates with repositories, and publishes events. This document details the patterns and practices used.

## Command Handler Architecture

### Core Handler Structure

```typescript
export class SessionCommandHandler {
  constructor(
    private readonly sessionRepository: SessionRepository,
    private readonly eventBus: EventBus,
  ) {}

  async handle(command: DomainCommand): Promise<SessionCommandResult> {
    try {
      // 1. Validate command structure
      this.validateCommand(command);
      
      // 2. Execute domain operation
      const result = await this.executeCommand(command);
      
      // 3. Publish events on success
      if (result.success) {
        await this.eventBus.publishEvents(result.events);
      }
      
      return result;
    } catch (error) {
      return this.handleError(error);
    }
  }
}
```

## Command Validation

### Structural Validation
```typescript
private validateCommand(command: DomainCommand): void {
  // Type guard validation
  if (!isDomainCommand(command)) {
    throw new Error('Invalid command structure');
  }

  // Required field validation
  if (!command.commandId) {
    throw new Error('Command ID not found in command');
  }

  // Additional validation as needed
  if (!command.issuedAt || command.issuedAt <= 0) {
    throw new Error('Invalid command timestamp');
  }
}
```

### Session ID Extraction
```typescript
private extractSessionId(command: DomainCommand): string {
  // All session commands should have sessionId in payload
  if (command && 'payload' in command && 'sessionId' in command.payload) {
    return command.payload.sessionId;
  }
  throw new Error('Session ID not found in command payload');
}
```

## Command Routing Patterns

### Kind-Based Routing
```typescript
private async executeCommand(command: DomainCommand): Promise<SessionCommandResult> {
  const sessionId = this.extractSessionId(command);

  switch (command.kind) {
    case 'CreateSession':
      return this.handleCreateSession(command);

    case 'StartSession':
    case 'PauseSession':
    case 'ResumeSession':
    case 'CompleteSession':
    case 'CancelSession':
      return this.handleSessionLifecycle(command, sessionId);

    case 'AddRound':
    case 'StartRound':
    case 'EndCurrentRound':
      return this.handleRoundManagement(command, sessionId);

    // ... other categories

    default:
      throw new Error(`Unknown command kind: ${command.kind}`);
  }
}
```

### Category-Based Handlers
```typescript
private async handleSessionLifecycle(
  command: DomainCommand,
  sessionId: string,
): Promise<SessionCommandResult> {
  return this.withSessionAggregate(command, sessionId, (session) => {
    switch (command.kind) {
      case 'StartSession':
        return session.startSession();
      case 'PauseSession':
        return session.pauseSession();
      case 'ResumeSession':
        return session.resumeSession();
      case 'CompleteSession':
        return session.completeSession();
      case 'CancelSession':
        return session.cancelSession();
      default:
        throw new Error(`Unhandled session lifecycle command: ${command.kind}`);
    }
  });
}
```

## Aggregate Loading and Saving

### Create Operations (New Aggregates)
```typescript
private async handleCreateSession(
  command: Extract<DomainCommand, { kind: 'CreateSession' }>,
): Promise<SessionCommandResult> {
  // Convert primitives to value objects
  const sessionId = SessionId.fromPrimitives(command.payload.sessionId);
  const config = SessionConfig.fromPrimitives(command.payload.config);
  const adminId = Uuid.fromPrimitives(command.payload.createdByUserId);
  const mainRoomId = RoomId.fromPrimitives(command.payload.mainRoomId);

  // Create new session aggregate
  const session = Session.create(sessionId, config, adminId, mainRoomId);

  // Get uncommitted events before saving
  const domainEvents = session.getUncommittedEvents();

  // Save the session
  await this.sessionRepository.save(session.toPrimitives());

  return {
    success: true,
    events: domainEvents,
    aggregateVersion: session.getVersion(),
  };
}
```

### Update Operations (Existing Aggregates)
```typescript
private async withSessionAggregate(
  command: DomainCommand,
  sessionId: string,
  operation: (session: Session) => Session,
): Promise<SessionCommandResult> {
  // Load existing aggregate
  const sessionDto = await this.sessionRepository.findById(sessionId);
  if (!sessionDto) {
    throw new Error(`Session not found: ${sessionId}`);
  }

  // Reconstruct domain object from primitives
  const session = Session.fromPrimitives(sessionDto);

  // Execute domain operation
  const updatedSession = operation(session);

  // Extract events and save
  const domainEvents = updatedSession.getUncommittedEvents();
  await this.sessionRepository.save(updatedSession.toPrimitives());

  return {
    success: true,
    events: domainEvents,
    aggregateVersion: updatedSession.getVersion(),
  };
}
```

## Specific Command Handlers

### Participant Management
```typescript
private async handleParticipantManagement(
  command: DomainCommand,
  sessionId: string,
): Promise<SessionCommandResult> {
  return this.withSessionAggregate(command, sessionId, (session) => {
    switch (command.kind) {
      case 'AddParticipant':
        return session.addParticipant(
          ParticipantId.fromPrimitives(command.payload.participantId),
          Instant.fromPrimitives(command.issuedAt),
          command.payload.role,
        );
      case 'JoinParticipant':
        return session.joinParticipant(
          ParticipantId.fromPrimitives(command.payload.participantId),
          Instant.fromPrimitives(command.issuedAt),
        );
      case 'SoftLeaveParticipant':
        return session.softLeaveParticipant(
          ParticipantId.fromPrimitives(command.payload.participantId),
          Instant.fromPrimitives(command.issuedAt),
        );
      case 'SeatParticipant':
        return session.seatParticipant(
          ParticipantId.fromPrimitives(command.payload.participantId),
          Instant.fromPrimitives(command.issuedAt),
        );
      default:
        throw new Error(`Unhandled participant management command: ${command.kind}`);
    }
  });
}
```

### Room Management
```typescript
private async handleRoomManagement(
  command: DomainCommand,
  sessionId: string,
): Promise<SessionCommandResult> {
  return this.withSessionAggregate(command, sessionId, (session) => {
    switch (command.kind) {
      case 'CreateBreakoutRoom':
        return session.createBreakoutRoom(
          RoomId.fromPrimitives(command.payload.roomId),
          Instant.fromPrimitives(command.issuedAt),
        );
      case 'AssignParticipantToRoom':
        return session.assignParticipantToRoom(
          ParticipantId.fromPrimitives(command.payload.participantId),
          RoomId.fromPrimitives(command.payload.roomId),
          Instant.fromPrimitives(command.issuedAt),
        );
      case 'ReleaseSeatByParticipant':
        return session.releaseSeatByParticipant(
          ParticipantId.fromPrimitives(command.payload.participantId),
          Instant.fromPrimitives(command.issuedAt),
        );
      case 'RotateBreakouts':
        return session.rotateBreakouts(
          Instant.fromPrimitives(command.issuedAt),
        );
      case 'ReleaseAllReservedSeats':
        return session.releaseAllReservedSeats(
          Instant.fromPrimitives(command.issuedAt),
        );
      default:
        throw new Error(`Unhandled room management command: ${command.kind}`);
    }
  });
}
```

## Error Handling

### Domain Error Propagation
```typescript
private handleError(error: unknown): FailedCommandResult {
  return {
    success: false,
    error: error instanceof Error ? error : new Error(String(error)),
  };
}
```

### Specific Error Types
- **Validation Errors**: Command structure or required field issues
- **Not Found Errors**: Aggregate doesn't exist
- **Domain Errors**: Business rule violations from aggregates
- **Infrastructure Errors**: Repository or event bus failures

## Event Publishing

### Successful Command Processing
```typescript
// Publish events if successful
if (result.success) {
  await this.eventBus.publishEvents(result.events);
}
```

### Event Bus Interface
```typescript
interface EventBus {
  publishEvents(events: readonly DomainEvent[]): Promise<void>;
}
```

## Testing Patterns

### Handler Testing
```typescript
describe('SessionCommandHandler', () => {
  let handler: SessionCommandHandler;
  let mockRepository: jest.Mocked<SessionRepository>;
  let mockEventBus: jest.Mocked<EventBus>;

  beforeEach(() => {
    mockRepository = {
      findById: jest.fn(),
      save: jest.fn(),
    };
    mockEventBus = {
      publishEvents: jest.fn(),
    };
    handler = new SessionCommandHandler(mockRepository, mockEventBus);
  });

  it('should publish events on successful command', async () => {
    // Arrange
    const command = createValidCommand();
    mockRepository.save.mockResolvedValue();

    // Act
    const result = await handler.handle(command);

    // Assert
    expect(result.success).toBe(true);
    expect(mockEventBus.publishEvents).toHaveBeenCalledWith(result.events);
  });
});
```

## Design Principles

### 1. Single Responsibility
Each handler method focuses on one command type or category.

### 2. Fail Fast
Validate commands early before expensive operations.

### 3. Consistent Patterns
All handlers follow the same structure and error handling.

### 4. Type Safety
Leverage TypeScript's type system for command routing and validation.

### 5. Testability
Design for easy mocking and unit testing.

This command handling system provides a robust, type-safe foundation for processing domain commands while maintaining clear separation of concerns and comprehensive error handling.
