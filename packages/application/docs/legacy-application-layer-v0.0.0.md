# Legacy Application Layer Documentation (v0.0.0)

> **Note**: This is the legacy documentation that was previously located in the domain package. 
> It has been moved here for reference but should be considered outdated.
> Please refer to the new application layer documentation files for current implementation details.

## 1) Envelopes — what they are (in plain words)

**Why:** we wrap every domain command/event so we can dedupe retries, trace what caused what, and log cleanly.

**CommandEnvelope (simple):**

```ts
{
  commandId: Uuid,         // unique per action; dedupe key
  correlationId: Uuid,     // groups everything from one action (thread id)
  causationId?: Uuid,      // id of the parent command/event
  issuedAt: Instant,       // server time (ms)
  actor: { role: 'SYSTEM'|'HOST'|'CO_HOST'|'ADMIN'|'ATTENDEE', participantId?: Uuid },
  payload: DomainCommand   // the actual domain input (pure)
}
```

**EventEnvelope (simple):**

```ts
{
  eventId: Uuid,
  sessionId: Uuid,
  roundId?: Uuid,
  roomId?: Uuid,
  occurredAt: Instant,     // server time (ms)
  causationId?: Uuid,      // producing command/event id
  correlationId: Uuid,     // copied from the command
  kind: string,            // e.g. 'SeatAssigned'
  payload: DomainEvent,
  version: 1
}
```

**Defaults to remember:**

* If you forget `correlationId`: set it to the `commandId`.
* Use **server** time for `issuedAt` / `occurredAt`, not client time.
* IDs must be UUID v4.

---

## 2) Correlation — the simplest rule that works

**Goal:** one id to grep to see *everything* that happened because of a single trigger.

**Create a new `correlationId` when:**

* A **user** clicks a button (root action).
* A **timer** fires (scheduled action).
* A **webhook** arrives (external trigger).

**Copy the parent `correlationId` when:**

* Have a parent? copy parent's `correlationId`.
* No parent? make a new one.
* Forgot? use `commandId` as `correlationId`.

**Tiny example:**

```
Host clicks "Start Round" → startRound (corr=A)
→ RoundStarted (corr=A)
→ createOrFillBreakoutRoom (corr=A)
→ SeatAssigned (corr=A)
```

---

## 3) Idempotency — how we avoid duplicates

**Why:** networks retry; we don't want double seating or double moves.

**Rules:**

* Every command **must** have a unique `commandId`.
* If the same `commandId` arrives again → **do nothing** additional and emit `IdempotencyReplayIgnored`.
* Keep a simple **ledger** `{ commandId -> applied }`.

**Natural guards already in domain help:**

* `seat.assign(participantId)` → no-op if already assigned to that participant.
* `session.start()` → no-op if already RUNNING.

---

## 4) Adapters — where we call external APIs

**Video Provider Adapter** listens for domain events:

* `RoomCreated` → cache `meetingId`.
* `SeatAssigned` → get `joinToken` + `meetingId`, join that meeting.
* `ParticipantMoved` → leave old meeting, join new with new token.
* `ParticipantsReturnedToMain` → join Main meeting.

**If adapter fails:** emit `SeatAssignmentFailed { reason }` once per `commandId`.

---

## 5) Logging — one format to rule them all

**Include these keys in every log line:**

* `correlationId`, `commandId` / `eventId`
* `sessionId`, `roundId`, `roomId` (when present)
* `commandName` / `eventKind`, `actor.role`

**Example:**

```
INFO correlation=corr-A cmd=c-1 name=startRound session=s-1
INFO correlation=corr-A evt=e-9  kind=RoundStarted session=s-1 round=rd-2
INFO correlation=corr-A evt=e-12 kind=SeatAssigned room=rm-7 seat=1 user=u-42
```

---

## 6) "Do / Don't" cheatsheet

**Do**

* Do create a **new** `correlationId` at root triggers (user/timer/webhook).
* Do **copy** the parent `correlationId` for derived work.
* Do use **the same** `commandId` when retrying.
* Do use **server** time only.
* Do make adapter calls **idempotent by `commandId`**.

**Don't**

* Don't put correlation logic inside domain entities. (It's app-layer.)
* Don't create new rooms in the provider without a matching `RoomCreated` event.
* Don't use client timestamps for timers or ordering.

---

This legacy documentation has been superseded by the new application layer architecture documentation. Please refer to:

- `application-layer-architecture.md` - Overall architecture and patterns
- `command-handling.md` - Command processing patterns
- `event-processing.md` - Event handling and integration
- `repository-patterns.md` - Data access patterns
