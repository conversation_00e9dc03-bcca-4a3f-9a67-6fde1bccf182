# Event Processing and Integration

## Overview

The application layer handles event processing through the EventBus interface, enabling integration with external systems and maintaining loose coupling between domain operations and infrastructure concerns.

## Event Bus Architecture

### Interface Definition
```typescript
interface EventBus {
  publishEvents(events: readonly DomainEvent[]): Promise<void>;
}
```

### Key Characteristics
- **Asynchronous**: Events are published asynchronously
- **Batch Processing**: Multiple events published together
- **Failure Isolation**: Event publishing failures don't affect command processing
- **Immutable Events**: Events are readonly after creation

## Event Publishing Flow

### 1. Command Processing
```typescript
async handle(command: DomainCommand): Promise<SessionCommandResult> {
  try {
    // Execute domain operation
    const result = await this.executeCommand(command);
    
    // Publish events if successful
    if (result.success) {
      await this.eventBus.publishEvents(result.events);
    }
    
    return result;
  } catch (error) {
    return this.handleError(error);
  }
}
```

### 2. Event Extraction
Events are extracted from domain aggregates after operations:
```typescript
// Get uncommitted events from aggregate
const domainEvents = session.getUncommittedEvents();

// Save aggregate state
await this.sessionRepository.save(session.toPrimitives());

// Return events for publishing
return {
  success: true,
  events: domainEvents,
  aggregateVersion: session.getVersion(),
};
```

### 3. Batch Publishing
```typescript
// Events are published as a batch
await this.eventBus.publishEvents([
  sessionCreatedEvent,
  mainRoomCreatedEvent,
  // ... other events from the operation
]);
```

## Event Categories and Use Cases

### Session Lifecycle Events
- **SessionCreated**: Initialize external resources
- **SessionStarted**: Begin monitoring and analytics
- **SessionPaused**: Suspend external integrations
- **SessionCompleted**: Cleanup and reporting
- **SessionCanceled**: Emergency cleanup

### Participant Events
- **ParticipantJoined**: User onboarding flows
- **ParticipantSeated**: Video conferencing integration
- **ParticipantMoved**: Update external meeting assignments
- **ParticipantLeft**: Cleanup user resources

### Room Events
- **BreakoutRoomCreated**: Provision video meeting rooms
- **RoomStateChanged**: Update capacity monitoring
- **SeatsReleased**: Free external resources

## Integration Patterns

### 1. Video Conferencing Integration
```typescript
class VideoConferencingEventHandler {
  async handle(event: DomainEvent): Promise<void> {
    switch (event.kind) {
      case 'BreakoutRoomCreated':
        await this.createMeetingRoom(event.payload);
        break;
      case 'ParticipantAssignedToRoom':
        await this.assignParticipantToMeeting(event.payload);
        break;
      case 'ParticipantMoved':
        await this.moveParticipantBetweenMeetings(event.payload);
        break;
    }
  }
}
```

### 2. Analytics and Monitoring
```typescript
class AnalyticsEventHandler {
  async handle(event: DomainEvent): Promise<void> {
    // Track session metrics
    await this.analytics.track(event.kind, {
      sessionId: this.extractSessionId(event),
      timestamp: event.occurredAt,
      payload: event.payload,
    });
  }
}
```

### 3. Notification Services
```typescript
class NotificationEventHandler {
  async handle(event: DomainEvent): Promise<void> {
    switch (event.kind) {
      case 'SessionStarted':
        await this.notifyParticipants(event.payload.sessionId);
        break;
      case 'RoundStarted':
        await this.notifyRoundBegin(event.payload);
        break;
    }
  }
}
```

## Event Bus Implementations

### 1. In-Memory Event Bus
```typescript
class InMemoryEventBus implements EventBus {
  private handlers: EventHandler[] = [];

  async publishEvents(events: readonly DomainEvent[]): Promise<void> {
    for (const event of events) {
      for (const handler of this.handlers) {
        try {
          await handler.handle(event);
        } catch (error) {
          // Log error but don't fail the entire batch
          console.error('Event handler failed:', error);
        }
      }
    }
  }

  addHandler(handler: EventHandler): void {
    this.handlers.push(handler);
  }
}
```

### 2. Message Queue Integration
```typescript
class MessageQueueEventBus implements EventBus {
  constructor(private messageQueue: MessageQueue) {}

  async publishEvents(events: readonly DomainEvent[]): Promise<void> {
    const messages = events.map(event => ({
      topic: `domain.${event.kind}`,
      payload: event,
      correlationId: event.correlationId,
    }));

    await this.messageQueue.publishBatch(messages);
  }
}
```

### 3. Event Store Integration
```typescript
class EventStoreEventBus implements EventBus {
  constructor(private eventStore: EventStore) {}

  async publishEvents(events: readonly DomainEvent[]): Promise<void> {
    // Store events for replay and audit
    await this.eventStore.append(events);
    
    // Also publish to real-time subscribers
    await this.publishToSubscribers(events);
  }
}
```

## Error Handling Strategies

### 1. Resilient Publishing
```typescript
class ResilientEventBus implements EventBus {
  async publishEvents(events: readonly DomainEvent[]): Promise<void> {
    for (const event of events) {
      try {
        await this.publishSingleEvent(event);
      } catch (error) {
        // Log error and continue with other events
        console.error(`Failed to publish event ${event.kind}:`, error);
        
        // Optionally queue for retry
        await this.queueForRetry(event, error);
      }
    }
  }
}
```

### 2. Dead Letter Queue
```typescript
class DeadLetterEventBus implements EventBus {
  async publishEvents(events: readonly DomainEvent[]): Promise<void> {
    for (const event of events) {
      try {
        await this.publishWithRetry(event);
      } catch (error) {
        // Send to dead letter queue after max retries
        await this.deadLetterQueue.send(event, error);
      }
    }
  }
}
```

## Event Correlation and Tracing

### Correlation ID Propagation
```typescript
// Events inherit correlation from commands
const event = createEvent('SessionCreated', payload, {
  correlationId: command.correlationId || command.commandId,
  causationId: command.commandId,
  occurredAt: Instant.now().toPrimitives(),
});
```

### Distributed Tracing
```typescript
class TracingEventBus implements EventBus {
  async publishEvents(events: readonly DomainEvent[]): Promise<void> {
    for (const event of events) {
      const span = this.tracer.startSpan(`event.${event.kind}`);
      span.setTag('correlationId', event.correlationId);
      span.setTag('sessionId', this.extractSessionId(event));
      
      try {
        await this.innerEventBus.publishEvents([event]);
        span.setTag('status', 'success');
      } catch (error) {
        span.setTag('status', 'error');
        span.setTag('error', error.message);
        throw error;
      } finally {
        span.finish();
      }
    }
  }
}
```

## Testing Event Processing

### 1. Event Bus Testing
```typescript
describe('EventBus', () => {
  let eventBus: InMemoryEventBus;
  let mockHandler: jest.Mocked<EventHandler>;

  beforeEach(() => {
    eventBus = new InMemoryEventBus();
    mockHandler = { handle: jest.fn() };
    eventBus.addHandler(mockHandler);
  });

  it('should publish events to all handlers', async () => {
    const events = [createTestEvent('SessionCreated')];
    
    await eventBus.publishEvents(events);
    
    expect(mockHandler.handle).toHaveBeenCalledWith(events[0]);
  });
});
```

### 2. Integration Testing
```typescript
describe('Command to Event Flow', () => {
  it('should publish events after successful command', async () => {
    const command = createTestCommand('CreateSession');
    const eventSpy = jest.spyOn(eventBus, 'publishEvents');
    
    const result = await commandHandler.handle(command);
    
    expect(result.success).toBe(true);
    expect(eventSpy).toHaveBeenCalledWith(
      expect.arrayContaining([
        expect.objectContaining({ kind: 'SessionCreated' })
      ])
    );
  });
});
```

## Performance Considerations

### 1. Async Processing
Events are processed asynchronously to avoid blocking command processing.

### 2. Batch Optimization
Multiple events from a single operation are published together.

### 3. Handler Isolation
Failed event handlers don't affect other handlers or command processing.

### 4. Resource Management
Event handlers should be lightweight and delegate heavy work to background services.

## Design Principles

### 1. Loose Coupling
Event publishers don't know about subscribers.

### 2. Eventual Consistency
External systems are updated asynchronously.

### 3. Fault Tolerance
Event processing failures don't affect domain operations.

### 4. Observability
Rich correlation and tracing support for debugging.

### 5. Scalability
Event processing can be distributed across multiple services.

This event processing architecture provides a robust foundation for integrating domain operations with external systems while maintaining system reliability and observability.
