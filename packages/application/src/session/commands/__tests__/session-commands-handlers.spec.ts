/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  DomainCommand,
  SessionPrimitives,
  SessionConfigPrimitives,
  SessionState,
  SessionMode,
  ParticipantRole,
  Uuid,
  Session,
  Instant,
  ParticipantId,
  PositiveInt,
} from '@repo/domain';
import { ActorRole } from '@repo/domain/contracts/commands/commands';
import { RoundKind } from '@repo/domain/value-objects/sessions/round/contracts/round-kind.enum';
import { AllocationStrategy } from '@repo/domain/policies/autopilot/types/allocation-strategy.enum';
import { LateJoinAllocationMode } from '@repo/domain/policies/late-join/types/late-join-allocation-mode.enum';
import { SessionCommandHandler } from '../session-commands-handlers';
import { SessionRepository } from '../../contracts/session-repository';
import { EventBus } from '../../../contracts/events-bus/event-bus.interface';
import { FailedCommandResult } from '../contracts/command-result.type';
import { Round } from '@repo/domain/value-objects/sessions/round/round.entity';

describe('SessionCommandHandler', () => {
  let handler: SessionCommandHandler;
  let mockSessionRepository: jest.Mocked<SessionRepository>;
  let mockEventBus: jest.Mocked<EventBus>;

  // Test data constants - using valid UUID v4 format
  const validSessionId = '550e8400-e29b-41d4-a716-************';
  const validAdminId = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
  const validMainRoomId = '6ba7b810-9dad-41d1-80b4-00c04fd430c8';
  const validParticipantId = '123e4567-e89b-42d3-a456-************';
  const validRoomId = '987fcdeb-51a2-43d1-9f12-************';
  const baseTimestamp = 1640995200000; // 2022-01-01 00:00:00 UTC

  const validSessionConfig: SessionConfigPrimitives = {
    scheduledStartAt: baseTimestamp + 300000,
    estimatedDurationMs: 3600000,
    defaultRoomConfig: {
      minSeats: 2,
      maxSeats: 8,
      avoidSingleton: true,
      disconnectionPolicy: { holdSeatForMs: 30000 },
    },
    maxParticipants: 50,
    defaultRoundDurationMs: 900000,
    autopilotPolicy: {
      allocationStategy: AllocationStrategy.ROUND_ROBIN,
    },
    lobbyAdmissionPolicy: {
      allowEarlyJoinMs: 300000,
      requireHostPresent: true,
    },
    disconnectionPolicy: {
      holdSeatForMs: 120000,
    },
    lateJoinPolicy: {
      allocationMode: LateJoinAllocationMode.BEST_FIT,
    },
    mode: SessionMode.HOSTED,
  };

  const createValidSessionPrimitives = (): SessionPrimitives => ({
    sessionId: validSessionId,
    config: validSessionConfig,
    state: SessionState.SCHEDULED,
    createdByUserId: validAdminId,
    hostId: undefined,
    mainRoomId: validMainRoomId,
    currentRoundNo: 0,
    rounds: [],
    participants: [],
    rooms: [
      {
        roomId: validMainRoomId,
        config: {
          minSeats: 2,
          maxSeats: 50,
          avoidSingleton: true,
          disconnectionPolicy: { holdSeatForMs: 120000 },
        },
        seats: Array.from({ length: 50 }, (_, i) => ({ seatNo: i })),
        createdAt: baseTimestamp,
      },
    ],
    createdAt: baseTimestamp,
    version: 0,
  });

  beforeEach(() => {
    mockSessionRepository = {
      findById: jest.fn(),
      save: jest.fn(),
    };

    mockEventBus = {
      publishEvents: jest.fn(),
    };

    handler = new SessionCommandHandler(mockSessionRepository, mockEventBus);
  });

  describe('handle', () => {
    it('publishes_events_on_successful_command', async () => {
      const command: DomainCommand = {
        kind: 'CreateSession',
        version: 1,
        commandId: 'cmd-123',
        issuedAt: baseTimestamp,
        actor: { actorId: validAdminId, role: ActorRole.ADMIN },
        payload: {
          sessionId: validSessionId,
          config: validSessionConfig,
          createdByUserId: validAdminId,
          mainRoomId: validMainRoomId,
        },
      };

      mockSessionRepository.save.mockResolvedValue();

      const result = await handler.handle(command);

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.events).toBeDefined();
        expect(result.aggregateVersion).toBeDefined();
        expect(mockEventBus.publishEvents).toHaveBeenCalledWith(result.events);
      }
    });

    it('returns_error_result_on_exception', async () => {
      const command: DomainCommand = {
        kind: 'CreateSession',
        version: 1,
        commandId: 'cmd-123',
        issuedAt: baseTimestamp,
        actor: { actorId: validAdminId, role: ActorRole.ADMIN },
        payload: {
          sessionId: validSessionId,
          config: validSessionConfig,
          createdByUserId: validAdminId,
          mainRoomId: validMainRoomId,
        },
      };

      const error = new Error('Repository error');
      mockSessionRepository.save.mockRejectedValue(error);

      const result = await handler.handle(command);

      expect(result.success).toBe(false);
      const failedResult = result as FailedCommandResult;
      expect(failedResult.error).toBe(error);
      expect(mockEventBus.publishEvents).not.toHaveBeenCalled();
    });

    it('does_not_publish_events_on_failed_command', async () => {
      const command: DomainCommand = {
        kind: 'StartSession',
        version: 1,
        commandId: 'cmd-123',
        issuedAt: baseTimestamp,
        actor: { actorId: validAdminId, role: ActorRole.ADMIN },
        payload: {
          sessionId: 'non-existent-session',
        },
      };

      mockSessionRepository.findById.mockResolvedValue(null);

      const result = await handler.handle(command);

      expect(result.success).toBe(false);
      expect(mockEventBus.publishEvents).not.toHaveBeenCalled();
    });
  });

  describe('CreateSession command', () => {
    it('creates_session_successfully', async () => {
      const command: DomainCommand = {
        kind: 'CreateSession',
        version: 1,
        commandId: 'cmd-123',
        issuedAt: baseTimestamp,
        actor: { actorId: validAdminId, role: ActorRole.ADMIN },
        payload: {
          sessionId: validSessionId,
          config: validSessionConfig,
          createdByUserId: validAdminId,
          mainRoomId: validMainRoomId,
        },
      };

      mockSessionRepository.save.mockResolvedValue();

      const result = await handler.handle(command);

      expect(result.success).toBe(true);
      expect(mockSessionRepository.save).toHaveBeenCalledTimes(1);

      if (result.success) {
        expect(result.events.length).toBeGreaterThan(0);
        expect(result.aggregateVersion).toBe(1);
      }
    });
  });

  describe('Session lifecycle commands', () => {
    beforeEach(() => {
      const sessionPrimitives = createValidSessionPrimitives();
      mockSessionRepository.findById.mockResolvedValue(sessionPrimitives);
      mockSessionRepository.save.mockResolvedValue();
    });

    it('handles_StartSession_command', async () => {
      const command: DomainCommand = {
        kind: 'StartSession',
        version: 1,
        commandId: 'cmd-123',
        issuedAt: baseTimestamp,
        actor: { actorId: validAdminId, role: ActorRole.ADMIN },
        payload: {
          sessionId: validSessionId,
        },
      };

      const result = await handler.handle(command);

      expect(result.success).toBe(true);
      expect(mockSessionRepository.findById).toHaveBeenCalledWith(
        validSessionId,
      );
      expect(mockSessionRepository.save).toHaveBeenCalledTimes(1);
    });

    it('handles_PauseSession_command', async () => {
      // First start the session
      const sessionPrimitives = createValidSessionPrimitives();
      sessionPrimitives.state = SessionState.RUNNING;
      mockSessionRepository.findById.mockResolvedValue(sessionPrimitives);

      const command: DomainCommand = {
        kind: 'PauseSession',
        version: 1,
        commandId: 'cmd-123',
        issuedAt: baseTimestamp,
        actor: { actorId: validAdminId, role: ActorRole.ADMIN },
        payload: {
          sessionId: validSessionId,
        },
      };

      const result = await handler.handle(command);

      expect(result.success).toBe(true);
      expect(mockSessionRepository.findById).toHaveBeenCalledWith(
        validSessionId,
      );
      expect(mockSessionRepository.save).toHaveBeenCalledTimes(1);
    });
  });

  describe('Round management commands', () => {
    beforeEach(() => {
      const sessionPrimitives = createValidSessionPrimitives();
      sessionPrimitives.state = SessionState.RUNNING;
      mockSessionRepository.findById.mockResolvedValue(sessionPrimitives);
      mockSessionRepository.save.mockResolvedValue();
    });

    it('handles_AddRound_command', async () => {
      const command: DomainCommand = {
        kind: 'AddRound',
        version: 1,
        commandId: 'cmd-123',
        issuedAt: baseTimestamp,
        actor: { actorId: validAdminId, role: ActorRole.ADMIN },
        payload: {
          sessionId: validSessionId,
          round: {
            roundNo: 1,
            kind: RoundKind.MAIN_TOPIC,
            durationMs: 900000,
            questions: ['Test question'],
          },
        },
      };

      const result = await handler.handle(command);

      expect(result.success).toBe(true);
      expect(mockSessionRepository.findById).toHaveBeenCalledWith(
        validSessionId,
      );
      expect(mockSessionRepository.save).toHaveBeenCalledTimes(1);
    });

    it('handles_StartRound_command', async () => {
      const sessionPrimitives = createValidSessionPrimitives();
      sessionPrimitives.state = SessionState.RUNNING;
      sessionPrimitives.rounds = [
        {
          roundNo: 1,
          kind: RoundKind.MAIN_TOPIC,
          durationMs: 900000,
          questions: ['Test question'],
        },
      ];
      mockSessionRepository.findById.mockResolvedValue(sessionPrimitives);

      const command: DomainCommand = {
        kind: 'StartRound',
        version: 1,
        commandId: 'cmd-123',
        issuedAt: baseTimestamp,
        actor: { actorId: validAdminId, role: ActorRole.ADMIN },
        payload: {
          sessionId: validSessionId,
          roundNo: 1,
        },
      };

      const result = await handler.handle(command);

      expect(result.success).toBe(true);
      expect(mockSessionRepository.findById).toHaveBeenCalledWith(
        validSessionId,
      );
      expect(mockSessionRepository.save).toHaveBeenCalledTimes(1);
    });
  });

  describe('Participant management commands', () => {
    beforeEach(() => {
      const sessionPrimitives = createValidSessionPrimitives();
      sessionPrimitives.state = SessionState.RUNNING;
      mockSessionRepository.findById.mockResolvedValue(sessionPrimitives);
      mockSessionRepository.save.mockResolvedValue();
    });

    it('handles_AddParticipant_command', async () => {
      const command: DomainCommand = {
        kind: 'AddParticipant',
        version: 1,
        commandId: 'cmd-123',
        issuedAt: baseTimestamp,
        actor: { actorId: validAdminId, role: ActorRole.ADMIN },
        payload: {
          sessionId: validSessionId,
          participantId: validParticipantId,
          participantRole: ParticipantRole.MEMBER,
        },
      };

      const result = await handler.handle(command);

      expect(result.success).toBe(true);
      expect(mockSessionRepository.findById).toHaveBeenCalledWith(
        validSessionId,
      );
      expect(mockSessionRepository.save).toHaveBeenCalledTimes(1);
    });

    it('handles_JoinParticipant_command', async () => {
      const command: DomainCommand = {
        kind: 'JoinParticipant',
        version: 1,
        commandId: 'cmd-123',
        issuedAt: baseTimestamp,
        actor: { actorId: validAdminId, role: ActorRole.ADMIN },
        payload: {
          sessionId: validSessionId,
          participantId: validParticipantId,
          participantRole: ParticipantRole.MEMBER,
        },
      };

      const result = await handler.handle(command);

      expect(result.success).toBe(true);
      expect(mockSessionRepository.findById).toHaveBeenCalledWith(
        validSessionId,
      );
      expect(mockSessionRepository.save).toHaveBeenCalledTimes(1);
    });

    it('handles_SoftLeaveParticipant_command', async () => {
      const command: DomainCommand = {
        kind: 'SoftLeaveParticipant',
        version: 1,
        commandId: 'cmd-123',
        issuedAt: baseTimestamp,
        actor: { actorId: validAdminId, role: ActorRole.ADMIN },
        payload: {
          sessionId: validSessionId,
          participantId: validParticipantId,
        },
      };

      const result = await handler.handle(command);

      expect(result.success).toBe(true);
      expect(mockSessionRepository.findById).toHaveBeenCalledWith(
        validSessionId,
      );
      expect(mockSessionRepository.save).toHaveBeenCalledTimes(1);
    });

    it('handles_SeatParticipant_command', async () => {
      // Add a participant to the session first
      const sessionPrimitives = createValidSessionPrimitives();
      sessionPrimitives.state = SessionState.RUNNING;
      sessionPrimitives.participants = [
        {
          participantId: validParticipantId,
          joinedAt: baseTimestamp,
          tags: { role: ParticipantRole.MEMBER },
        },
      ];
      mockSessionRepository.findById.mockResolvedValue(sessionPrimitives);

      const command: DomainCommand = {
        kind: 'SeatParticipant',
        version: 1,
        commandId: 'cmd-123',
        issuedAt: baseTimestamp,
        actor: { actorId: validAdminId, role: ActorRole.ADMIN },
        payload: {
          sessionId: validSessionId,
          participantId: validParticipantId,
        },
      };

      const result = await handler.handle(command);

      expect(result.success).toBe(true);
      expect(mockSessionRepository.findById).toHaveBeenCalledWith(
        validSessionId,
      );
      expect(mockSessionRepository.save).toHaveBeenCalledTimes(1);
    });
  });

  describe('Host management commands', () => {
    beforeEach(() => {
      const sessionPrimitives = createValidSessionPrimitives();
      sessionPrimitives.state = SessionState.RUNNING;
      mockSessionRepository.findById.mockResolvedValue(sessionPrimitives);
      mockSessionRepository.save.mockResolvedValue();
    });

    it('handles_SetHost_command', async () => {
      // Add a participant to the session first
      const sessionPrimitives = createValidSessionPrimitives();
      sessionPrimitives.state = SessionState.RUNNING;
      sessionPrimitives.participants = [
        {
          participantId: validParticipantId,
          joinedAt: baseTimestamp,
          tags: { role: ParticipantRole.HOST },
        },
      ];
      mockSessionRepository.findById.mockResolvedValue(sessionPrimitives);

      const command: DomainCommand = {
        kind: 'SetHost',
        version: 1,
        commandId: 'cmd-123',
        issuedAt: baseTimestamp,
        actor: { actorId: validAdminId, role: ActorRole.ADMIN },
        payload: {
          sessionId: validSessionId,
          hostId: validParticipantId,
        },
      };

      const result = await handler.handle(command);

      expect(result.success).toBe(true);
      expect(mockSessionRepository.findById).toHaveBeenCalledWith(
        validSessionId,
      );
      expect(mockSessionRepository.save).toHaveBeenCalledTimes(1);
    });
  });

  describe('Room management commands', () => {
    beforeEach(() => {
      const sessionPrimitives = createValidSessionPrimitives();
      sessionPrimitives.state = SessionState.RUNNING;
      mockSessionRepository.findById.mockResolvedValue(sessionPrimitives);
      mockSessionRepository.save.mockResolvedValue();
    });

    it('handles_CreateBreakoutRoom_command', async () => {
      const command: DomainCommand = {
        kind: 'CreateBreakoutRoom',
        version: 1,
        commandId: 'cmd-123',
        issuedAt: baseTimestamp,
        actor: { actorId: validAdminId, role: ActorRole.ADMIN },
        payload: {
          sessionId: validSessionId,
          roomId: validRoomId,
        },
      };

      const result = await handler.handle(command);

      expect(result.success).toBe(true);
      expect(mockSessionRepository.findById).toHaveBeenCalledWith(
        validSessionId,
      );
      expect(mockSessionRepository.save).toHaveBeenCalledTimes(1);
    });

    it('handles_AssignParticipantToRoom_command', async () => {
      // Add a participant and room to the session first
      const sessionPrimitives = createValidSessionPrimitives();
      sessionPrimitives.state = SessionState.RUNNING;
      sessionPrimitives.participants = [
        {
          participantId: validParticipantId,
          joinedAt: baseTimestamp,
          tags: { role: ParticipantRole.MEMBER },
        },
      ];
      sessionPrimitives.rooms.push({
        roomId: validRoomId,
        config: {
          minSeats: 2,
          maxSeats: 8,
          avoidSingleton: true,
          disconnectionPolicy: { holdSeatForMs: 30000 },
        },
        seats: Array.from({ length: 8 }, (_, i) => ({ seatNo: i })),
        createdAt: baseTimestamp,
      });
      mockSessionRepository.findById.mockResolvedValue(sessionPrimitives);

      const command: DomainCommand = {
        kind: 'AssignParticipantToRoom',
        version: 1,
        commandId: 'cmd-123',
        issuedAt: baseTimestamp,
        actor: { actorId: validAdminId, role: ActorRole.ADMIN },
        payload: {
          sessionId: validSessionId,
          participantId: validParticipantId,
          roomId: validRoomId,
        },
      };

      const result = await handler.handle(command);

      expect(result.success).toBe(true);
      expect(mockSessionRepository.findById).toHaveBeenCalledWith(
        validSessionId,
      );
      expect(mockSessionRepository.save).toHaveBeenCalledTimes(1);
    });

    it('handles_ReleaseSeatByParticipant_command', async () => {
      // Add a participant with a seat to the session first
      const sessionPrimitives = createValidSessionPrimitives();
      sessionPrimitives.state = SessionState.RUNNING;
      sessionPrimitives.participants = [
        {
          participantId: validParticipantId,
          joinedAt: baseTimestamp,
          tags: { role: ParticipantRole.MEMBER },
        },
      ];
      // Add a seat assignment to the main room
      sessionPrimitives.rooms[0].seats[0] = {
        seatNo: 0,
        participantId: validParticipantId,
        assignedAt: baseTimestamp,
      };
      mockSessionRepository.findById.mockResolvedValue(sessionPrimitives);

      const command: DomainCommand = {
        kind: 'ReleaseSeatByParticipant',
        version: 1,
        commandId: 'cmd-123',
        issuedAt: baseTimestamp,
        actor: { actorId: validAdminId, role: ActorRole.ADMIN },
        payload: {
          sessionId: validSessionId,
          participantId: validParticipantId,
        },
      };

      const result = await handler.handle(command);

      expect(result.success).toBe(true);
      expect(mockSessionRepository.findById).toHaveBeenCalledWith(
        validSessionId,
      );
      expect(mockSessionRepository.save).toHaveBeenCalledTimes(1);
    });
    it('handles_RotateBreakouts_command', async () => {
      const mkPid = () => Uuid.generate();
      const at = baseTimestamp;

      // --- Start from valid primitives then hydrate to aggregate
      const seed = createValidSessionPrimitives();
      seed.state = SessionState.RUNNING;
      seed.config.defaultRoomConfig.maxSeats = 4;

      // hydrate (name may be Session.fromPrimitives / restore / rehydrate in your codebase)
      let session = Session.fromPrimitives(seed);

      // --- Ensure rotation preconditions (active round)
      const round = Round.fromPrimitives({
        roundNo: 1,
        kind: RoundKind.MAIN_TOPIC,
        durationMs: 900000,
        questions: ['Q1'],
      });
      session = session.addRound(round);
      session = session.startRound(PositiveInt.fromPrimitives(1));


      // --- Join 8 participants
      const participantIds = Array.from({ length: 8 }, () =>
        ParticipantId.fromPrimitives(mkPid()),
      );
      for (const pid of participantIds) {
        session = session.onJoin(pid, Instant.fromPrimitives(at));
      }


      // --- Now persistable primitives (seats are occupied correctly)
      const hydratedPrimitives = session.toPrimitives();

      mockSessionRepository.findById.mockResolvedValue(hydratedPrimitives);

      const command: DomainCommand = {
        kind: 'RotateBreakouts',
        version: 1,
        commandId: 'cmd-123',
        issuedAt: at,
        actor: { actorId: validAdminId, role: ActorRole.ADMIN },
        payload: { sessionId: validSessionId },
      };

      const result = await handler.handle(command);

      expect(result.success).toBe(true);
      expect(mockSessionRepository.findById).toHaveBeenCalledWith(
        validSessionId,
      );
      expect(mockSessionRepository.save).toHaveBeenCalledTimes(1);
      expect(mockEventBus.publishEvents).toHaveBeenCalledTimes(1);
    });

    it('handles_ReleaseAllReservedSeats_command', async () => {
      const command: DomainCommand = {
        kind: 'ReleaseAllReservedSeats',
        version: 1,
        commandId: 'cmd-123',
        issuedAt: baseTimestamp,
        actor: { actorId: validAdminId, role: ActorRole.ADMIN },
        payload: {
          sessionId: validSessionId,
        },
      };

      const result = await handler.handle(command);

      expect(result.success).toBe(true);
      expect(mockSessionRepository.findById).toHaveBeenCalledWith(
        validSessionId,
      );
      expect(mockSessionRepository.save).toHaveBeenCalledTimes(1);
    });
  });

  describe('Error handling', () => {
    it('throws_error_for_invalid_command', async () => {
      const command = {
        kind: 'UnknownCommand',
        version: 1,
        commandId: 'cmd-123',
        issuedAt: baseTimestamp,
        actor: { actorId: validAdminId, role: ActorRole.ADMIN },
        payload: {
          sessionId: validSessionId,
        },
      } as any;

      const result = await handler.handle(command);

      expect(result.success).toBe(false);
      const failedResult = result as FailedCommandResult;
      expect(failedResult.error.message).toContain('Invalid command');
    });

    it('throws_error_when_session_not_found', async () => {
      mockSessionRepository.findById.mockResolvedValue(null);

      const command: DomainCommand = {
        kind: 'StartSession',
        version: 1,
        commandId: 'cmd-123',
        issuedAt: baseTimestamp,
        actor: { actorId: validAdminId, role: ActorRole.ADMIN },
        payload: {
          sessionId: 'non-existent-session',
        },
      };

      const result = await handler.handle(command);

      expect(result.success).toBe(false);
      const failedResult = result as FailedCommandResult;
      expect(failedResult.error.message).toContain(
        'Session DTO is null or undefined',
      );
    });
    it('throws_error_when_commandId_not_provided', async () => {
      const command: DomainCommand = {
        kind: 'StartSession',
        version: 1,
        commandId: '',
        issuedAt: baseTimestamp,
        actor: { actorId: validAdminId, role: ActorRole.ADMIN },
        payload: {
          sessionId: validSessionId,
        },
      };

      const result = await handler.handle(command);

      expect(result.success).toBe(false);
      const failedResult = result as FailedCommandResult;
      expect(failedResult.error.message).toContain(
        'Command ID not found in command',
      );
    });

    it('throws_error_when_command_missing_sessionId', async () => {
      const command = {
        kind: 'StartSession',
        version: 1,
        commandId: 'cmd-123',
        issuedAt: baseTimestamp,
        actor: { actorId: validAdminId, role: ActorRole.ADMIN },
        payload: {
          // Missing sessionId
        },
      } as any;

      const result = await handler.handle(command);

      expect(result.success).toBe(false);
      const failedResult = result as FailedCommandResult;
      expect(failedResult.error.message).toContain(
        'Session ID not found in command payload',
      );
    });

    it('validates_expected_version_when_provided', async () => {
      const sessionPrimitives = createValidSessionPrimitives();
      sessionPrimitives.version = 5;
      mockSessionRepository.findById.mockResolvedValue(sessionPrimitives);

      const command: DomainCommand = {
        kind: 'StartSession',
        version: 1,
        expectedVersion: 3, // Different from actual version
        commandId: 'cmd-123',
        issuedAt: baseTimestamp,
        actor: { actorId: validAdminId, role: ActorRole.ADMIN },
        payload: {
          sessionId: validSessionId,
        },
      };

      const result = await handler.handle(command);

      expect(result.success).toBe(false);
      // The error should be related to version mismatch
    });
  });
});
