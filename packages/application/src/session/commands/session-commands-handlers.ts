import {
  Session,
  SessionId,
  SessionConfig,
  Uuid,
  RoomId,
  Instant,
  ParticipantId,
  PositiveInt,
  DomainCommand,
  isDomainCommand,
} from '@repo/domain';
import { Round } from '@repo/domain/value-objects/sessions/round/round.entity';
import { SessionRepository } from '../contracts/session-repository';
import {
  FailedCommandResult,
  SessionCommandResult,
} from './contracts/command-result.type';
import { EventBus } from 'contracts/events-bus/event-bus.interface';


// TODO : We need to use strategies for each command type (Strategy pattern)

// ============================================================================
// Session Command Handler
// ============================================================================

export class SessionCommandHandler {
  constructor(
    private readonly sessionRepository: SessionRepository,
    private readonly eventBus: EventBus,
  ) {}

  async handle(command: DomainCommand): Promise<SessionCommandResult> {
    try {

      // Validate command
      if (!isDomainCommand(command)) {
        throw new Error('Invalid command');
      }

      if(!command.commandId) {
        throw new Error('Command ID not found in command');
      }

      const result = await this.executeCommand(command);

      // Publish events if successful
      if (result.success) {
        await this.eventBus.publishEvents(result.events);
      }

      return result;
    } catch (error) {
      const result: FailedCommandResult = {
        success: false,
        error: error instanceof Error ? error : new Error(String(error)),
      };

      return result;
    }
  }

  private async executeCommand(
    command: DomainCommand,
  ): Promise<SessionCommandResult> {
    const sessionId = this.extractSessionId(command);

    switch (command.kind) {
      case 'CreateSession':
        return this.handleCreateSession(command);

      case 'StartSession':
      case 'PauseSession':
      case 'ResumeSession':
      case 'CompleteSession':
      case 'CancelSession':
        return this.handleSessionLifecycle(command, sessionId);

      case 'AddRound':
      case 'StartRound':
      case 'EndCurrentRound':
        return this.handleRoundManagement(command, sessionId);

      case 'AddParticipant':
      case 'JoinParticipant':
      case 'SoftLeaveParticipant':
      case 'SeatParticipant':
        return this.handleParticipantManagement(command, sessionId);

      case 'SetHost':
        return this.handleHostManagement(command, sessionId);

      case 'CreateBreakoutRoom':
      case 'AssignParticipantToRoom':
      case 'ReleaseSeatByParticipant':
      case 'RotateBreakouts':
      case 'ReleaseAllReservedSeats':
        return this.handleRoomManagement(command, sessionId);

      default:
        throw new Error(`Unknown command kind: ${command.kind}`);
    }
  }

  private async handleCreateSession(
    command: Extract<DomainCommand, { kind: 'CreateSession' }>,
  ): Promise<SessionCommandResult> {
    // Convert primitives to value objects
    const sessionId = SessionId.fromPrimitives(command.payload.sessionId);
    const config = SessionConfig.fromPrimitives(command.payload.config);
    const adminId = Uuid.fromPrimitives(command.payload.createdByUserId);
    const mainRoomId = RoomId.fromPrimitives(command.payload.mainRoomId);

    // Create new session
    const session = Session.create(sessionId, config, adminId, mainRoomId);

    // Get uncommitted events before saving
    const domainEvents = session.getUncommittedEvents();

    // Save the session (this should mark events as committed)
    await this.sessionRepository.save(session.toPrimitives());

    return {
      success: true,
      events: domainEvents,
      aggregateVersion: session.getVersion(),
    };
  }

  private async handleSessionLifecycle(
    command: DomainCommand,
    sessionId: string,
  ): Promise<SessionCommandResult> {
    return this.withSessionAggregate(command, sessionId, (session) => {
      switch (command.kind) {
        case 'StartSession':
          return session.startSession();
        case 'PauseSession':
          return session.pauseSession();
        case 'ResumeSession':
          return session.resumeSession();
        case 'CompleteSession':
          return session.completeSession();
        case 'CancelSession':
          return session.cancelSession();
        default:
          throw new Error(
            `Unhandled session lifecycle command: ${command.kind}`,
          );
      }
    });
  }

  private async handleRoundManagement(
    command: DomainCommand,
    sessionId: string,
  ): Promise<SessionCommandResult> {
    return this.withSessionAggregate(command, sessionId, (session) => {
      switch (command.kind) {
        case 'AddRound':
          return session.addRound(Round.fromPrimitives(command.payload.round));
        case 'StartRound':
          return session.startRound(
            PositiveInt.fromPrimitives(command.payload.roundNo),
          );
        case 'EndCurrentRound':
          return session.endCurrentRound();
        default:
          throw new Error(
            `Unhandled round management command: ${command.kind}`,
          );
      }
    });
  }

  private async handleParticipantManagement(
    command: DomainCommand,
    sessionId: string,
  ): Promise<SessionCommandResult> {
    return this.withSessionAggregate(command, sessionId, (session) => {
      switch (command.kind) {
        case 'AddParticipant':
          return session.addParticipant(
            ParticipantId.fromPrimitives(command.payload.participantId),
            Instant.fromPrimitives(command.issuedAt),
            command.payload.participantRole,
          );
        case 'JoinParticipant':
          return session.onJoin(
            ParticipantId.fromPrimitives(command.payload.participantId),
            Instant.fromPrimitives(command.issuedAt),
            command.payload.participantRole,
          );
        case 'SoftLeaveParticipant':
          return session.onSoftLeave(
            ParticipantId.fromPrimitives(command.payload.participantId),
            Instant.fromPrimitives(command.issuedAt),
          );
        case 'SeatParticipant':
          return session.seatParticipant(
            ParticipantId.fromPrimitives(command.payload.participantId),
            Instant.fromPrimitives(command.issuedAt),
          );
        default:
          throw new Error(
            `Unhandled participant management command: ${command.kind}`,
          );
      }
    });
  }

  private async handleHostManagement(
    command: DomainCommand,
    sessionId: string,
  ): Promise<SessionCommandResult> {
    return this.withSessionAggregate(command, sessionId, (session) => {
      switch (command.kind) {
        case 'SetHost':
          return session.setHost(
            ParticipantId.fromPrimitives(command.payload.hostId),
          );
        default:
          throw new Error(`Unhandled host management command: ${command.kind}`);
      }
    });
  }

  private async handleRoomManagement(
    command: DomainCommand,
    sessionId: string,
  ): Promise<SessionCommandResult> {
    return this.withSessionAggregate(command, sessionId, (session) => {
      switch (command.kind) {
        case 'CreateBreakoutRoom':
          return session.createBreakoutRoom(
            RoomId.fromPrimitives(command.payload.roomId),
            Instant.fromPrimitives(command.issuedAt),
          );
        case 'AssignParticipantToRoom':
          return session.assignParticipantToRoom(
            ParticipantId.fromPrimitives(command.payload.participantId),
            RoomId.fromPrimitives(command.payload.roomId),
            Instant.fromPrimitives(command.issuedAt),
          );
        case 'ReleaseSeatByParticipant':
          return session.releaseSeatByParticipant(
            ParticipantId.fromPrimitives(command.payload.participantId),
            Instant.fromPrimitives(command.issuedAt),
          );
        case 'RotateBreakouts':
          return session.rotateBreakouts(
            Instant.fromPrimitives(command.issuedAt),
          );
        case 'ReleaseAllReservedSeats':
          return session.releaseAllReservedSeats(
            Instant.fromPrimitives(command.issuedAt),
          );
        default:
          throw new Error(`Unhandled room management command: ${command.kind}`);
      }
    });
  }

  private async withSessionAggregate(
    command: DomainCommand,
    sessionId: string,
    operation: (session: Session) => Session,
  ): Promise<SessionCommandResult> {
    // Load session
    const sessionPrimitives = await this.sessionRepository.findById(sessionId);
    const session = Session.fromPrimitives(sessionPrimitives);
    if (!session) {
      throw new Error(`Session not found: ${sessionId}`);
    }

    // Check expected version if provided in command
    if ('expectedVersion' in command && command.expectedVersion !== undefined) {
      session.validateExpectedVersion(command.expectedVersion);
    }

    // Execute operation
    const updatedSession = operation(session);

    // Save and get events
    const domainEvents = updatedSession.getUncommittedEvents();
    await this.sessionRepository.save(updatedSession.toPrimitives());

    return {
      success: true,
      events: domainEvents,
      aggregateVersion: updatedSession.getVersion(),
    };
  }

  private extractSessionId(command: DomainCommand): string {
    // All session commands should have sessionId in payload
    if (command && 'payload' in command && 'sessionId' in command.payload) {
      return command.payload.sessionId;
    }
    throw new Error('Session ID not found in command payload');
  }
}
