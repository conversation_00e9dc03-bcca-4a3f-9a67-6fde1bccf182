// Session Command Handling
export * from './session/commands/session-commands-handlers';

// application/
// ├── commands/
// │   ├── session/
// │   │   ├── start-session.command.ts
// │   │   ├── end-session.command.ts
// │   │   └── index.ts
// │   ├── participant/
// │   │   ├── join-session.command.ts
// │   │   ├── leave-session.command.ts
// │   │   └── index.ts
// │   └── index.ts
// ├── queries/
// │   ├── session/
// │   │   ├── get-session.query.ts
// │   │   ├── list-sessions.query.ts
// │   │   └── index.ts
// │   └── index.ts
// ├── handlers/
// │   ├── command-handlers/
// │   │   ├── session.handler.ts
// │   │   ├── participant.handler.ts
// │   │   └── index.ts
// │   ├── query-handlers/
// │   │   ├── session.handler.ts
// │   │   └── index.ts
// │   └── index.ts
// ├── envelopes/           # ✅ Already exists
// ├── events/
// │   ├── domain-events/
// │   │   ├── session-started.event.ts
// │   │   └── index.ts
// │   └── index.ts
// ├── services/
// │   ├── session.service.ts
// │   └── index.ts
// └── contracts/
//     ├── repositories/
//     │   ├── session.repository.ts
//     │   └── index.ts
//     └── index.ts