import { NonNegativeInt } from '../../primitives/non-negative-int/non-negative-int.primitive';
import { LobbyAdmissionPolicyPrimitives } from './types/lobby-admission';



export class LobbyAdmissionPolicy {
  private constructor(
    private readonly _allowEarlyJoinMs: number,
    private readonly _requireHostPresent: boolean
  ) {}

  static fromPrimitives(raw: LobbyAdmissionPolicyPrimitives): LobbyAdmissionPolicy {
    const allowEarlyJoinMs = NonNegativeInt.fromPrimitives(raw.allowEarlyJoinMs).toPrimitives();
    const requireHostPresent = raw.requireHostPresent;

    return new LobbyAdmissionPolicy(allowEarlyJoinMs, requireHostPresent);
  }

  toPrimitives(): LobbyAdmissionPolicyPrimitives {
    return {
      allowEarlyJoinMs: this._allowEarlyJoinMs,
      requireHostPresent: this._requireHostPresent
    };
  }

  get allowEarlyJoinMs(): number {
    return this._allowEarlyJoinMs;
  }

  get requireHostPresent(): boolean {
    return this._requireHostPresent;
  }
}
