import { LobbyAdmissionPolicy } from '../lobby-admission.vo';
import { LobbyAdmissionPolicyPrimitives } from '../types/lobby-admission';

describe('LobbyAdmissionPolicy', () => {
  describe('fromPrimitives - valid cases', () => {
    it('should accept allowEarlyJoinMs=0, requireHostPresent=true', () => {
      const primitives: LobbyAdmissionPolicyPrimitives = {
        allowEarlyJoinMs: 0,
        requireHostPresent: true
      };
      
      const policy = LobbyAdmissionPolicy.fromPrimitives(primitives);
      
      expect(policy.allowEarlyJoinMs).toBe(0);
      expect(policy.requireHostPresent).toBe(true);
    });

    it('should accept allowEarlyJoinMs=300000, requireHostPresent=false', () => {
      const primitives: LobbyAdmissionPolicyPrimitives = {
        allowEarlyJoinMs: 300000,
        requireHostPresent: false
      };
      
      const policy = LobbyAdmissionPolicy.fromPrimitives(primitives);
      
      expect(policy.allowEarlyJoinMs).toBe(300000);
      expect(policy.requireHostPresent).toBe(false);
    });

    it('should accept large allowEarlyJoinMs values', () => {
      const primitives: LobbyAdmissionPolicyPrimitives = {
        allowEarlyJoinMs: 3600000, // 1 hour
        requireHostPresent: true
      };
      
      const policy = LobbyAdmissionPolicy.fromPrimitives(primitives);
      
      expect(policy.allowEarlyJoinMs).toBe(3600000);
    });
  });

  describe('fromPrimitives - invalid cases', () => {
    it('should reject negative allowEarlyJoinMs', () => {
      const primitives: LobbyAdmissionPolicyPrimitives = {
        allowEarlyJoinMs: -1,
        requireHostPresent: true
      };
      
      // NonNegativeInt validation will throw before our custom validation
      expect(() => LobbyAdmissionPolicy.fromPrimitives(primitives)).toThrow();
    });

    it('should reject non-integer allowEarlyJoinMs', () => {
      const primitives: LobbyAdmissionPolicyPrimitives = {
        allowEarlyJoinMs: 1.5,
        requireHostPresent: true
      };
      
      // NonNegativeInt validation will throw
      expect(() => LobbyAdmissionPolicy.fromPrimitives(primitives)).toThrow();
    });
  });

  describe('toPrimitives', () => {
    it('should serialize correctly', () => {
      const primitives: LobbyAdmissionPolicyPrimitives = {
        allowEarlyJoinMs: 30000,
        requireHostPresent: false
      };
      
      const policy = LobbyAdmissionPolicy.fromPrimitives(primitives);
      
      expect(policy.toPrimitives()).toEqual(primitives);
    });
  });

  describe('roundtrip', () => {
    it('should maintain equality after roundtrip', () => {
      const original: LobbyAdmissionPolicyPrimitives = {
        allowEarlyJoinMs: 120000,
        requireHostPresent: true
      };
      
      const policy = LobbyAdmissionPolicy.fromPrimitives(original);
      const roundtrip = policy.toPrimitives();
      
      expect(roundtrip).toEqual(original);
    });
  });

  describe('getters', () => {
    it('should provide access to allowEarlyJoinMs', () => {
      const policy = LobbyAdmissionPolicy.fromPrimitives({
        allowEarlyJoinMs: 60000,
        requireHostPresent: false
      });
      
      expect(policy.allowEarlyJoinMs).toBe(60000);
    });

    it('should provide access to requireHostPresent', () => {
      const policy = LobbyAdmissionPolicy.fromPrimitives({
        allowEarlyJoinMs: 0,
        requireHostPresent: true
      });
      
      expect(policy.requireHostPresent).toBe(true);
    });
  });
});
