import { ensure } from '../../support/ensure';
import { AutopilotPolicyInvalidError } from './autopilot-policy.errors';
import { AllocationStrategy } from './types/allocation-strategy.enum';
import { AutopilotPolicyPrimitives } from './types/auto-pilote.policy';


export class AutopilotPolicy {
  private constructor(private readonly _strategy: AllocationStrategy) {}

  static fromPrimitives(raw: AutopilotPolicyPrimitives): AutopilotPolicy {
    // Validate that literals match allowed sets
    ensure(
      raw.allocationStategy === AllocationStrategy.ROUND_ROBIN ||
        raw.allocationStategy === AllocationStrategy.FILL_SMALLEST_FIRST,
      new AutopilotPolicyInvalidError({
        strategy: raw.allocationStategy,
        reason: 'strategy must be ROUND_ROBIN or FILL_SMALLEST_FIRST',
      }),
    );

    return new AutopilotPolicy(raw.allocationStategy);
  }

  toPrimitives(): AutopilotPolicyPrimitives {
    return {
      allocationStategy: this._strategy,
    };
  }

  get strategy(): AllocationStrategy {
    return this._strategy;
  }
}
