import { AutopilotPolicy } from '../autopilot-policy.vo';
import { AutopilotPolicyInvalidError, ERR_POLICY_AUTOPILOT_INVALID } from '../autopilot-policy.errors';
import { AllocationStrategy } from '../types/allocation-strategy.enum';
import { AutopilotPolicyPrimitives } from '../types/auto-pilote.policy';

describe('AutopilotPolicy', () => {
  describe('fromPrimitives - valid cases', () => {
    it('should accept ROUND_ROBIN', () => {
      const primitives: AutopilotPolicyPrimitives = {
        allocationStategy: AllocationStrategy.ROUND_ROBIN,
      };
      
      const policy = AutopilotPolicy.fromPrimitives(primitives);
      
      expect(policy.strategy).toBe('ROUND_ROBIN');
    });

    it('should accept FILL_SMALLEST_FIRST', () => {
      const primitives: AutopilotPolicyPrimitives = {
        allocationStategy: AllocationStrategy.FILL_SMALLEST_FIRST,
      };
      
      const policy = AutopilotPolicy.fromPrimitives(primitives);
      
      expect(policy.strategy).toBe('FILL_SMALLEST_FIRST');
    });
  });

  describe('fromPrimitives - invalid cases', () => {
    it('should reject invalid strategy', () => {
      const primitives = {
        allocationStategy: 'INVALID_STRATEGY' as AllocationStrategy,
      };
      
      expect(() => AutopilotPolicy.fromPrimitives(primitives)).toThrow(AutopilotPolicyInvalidError);

      try {

        AutopilotPolicy.fromPrimitives(primitives);
        
      } catch (error) {

        expect(error).toBeInstanceOf(AutopilotPolicyInvalidError);
        expect((error as AutopilotPolicyInvalidError).code).toBe(ERR_POLICY_AUTOPILOT_INVALID);
        expect((error as AutopilotPolicyInvalidError).ctx?.strategy).toBe('INVALID_STRATEGY');
        expect((error as AutopilotPolicyInvalidError).ctx?.reason).toBe('strategy must be ROUND_ROBIN or FILL_SMALLEST_FIRST');
        
      }
    });
  });

  describe('toPrimitives', () => {
    it('should serialize correctly', () => {
      const primitives: AutopilotPolicyPrimitives = {
        allocationStategy: AllocationStrategy.FILL_SMALLEST_FIRST,
      };
      
      const policy = AutopilotPolicy.fromPrimitives(primitives);
      
      expect(policy.toPrimitives()).toEqual(primitives);
    });
  });

  describe('roundtrip', () => {
    it('should maintain equality after roundtrip', () => {
      const original: AutopilotPolicyPrimitives = {
        allocationStategy: AllocationStrategy.ROUND_ROBIN,
      };
      
      const policy = AutopilotPolicy.fromPrimitives(original);
      const roundtrip = policy.toPrimitives();
      
      expect(roundtrip).toEqual(original);
    });
  });

  describe('getters', () => {
    it('should provide access to all properties', () => {
      const policy = AutopilotPolicy.fromPrimitives({
        allocationStategy: AllocationStrategy.FILL_SMALLEST_FIRST,
      });
      
      expect(policy.strategy).toBe('FILL_SMALLEST_FIRST');
    });
  });
});
