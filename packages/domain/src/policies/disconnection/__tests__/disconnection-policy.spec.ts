import { DisconnectionPolicy } from '../disconnection-policy.vo';
import { DisconnectionPolicyPrimitives } from '../types/disconnection.policy';

describe('DisconnectionPolicy', () => {
  describe('fromPrimitives - valid cases', () => {
    it('should accept holdSeatForMs=0', () => {
      const primitives: DisconnectionPolicyPrimitives = {
        holdSeatForMs: 0,
      };
      
      const policy = DisconnectionPolicy.fromPrimitives(primitives);
      
      expect(policy.holdSeatForMs).toBe(0);
    });

    it('should accept holdSeatForMs=300000', () => {
      const primitives: DisconnectionPolicyPrimitives = {
        holdSeatForMs: 300000,
      };
      
      const policy = DisconnectionPolicy.fromPrimitives(primitives);
      
      expect(policy.holdSeatForMs).toBe(300000);
    });

    it('should accept large holdSeatForMs values', () => {
      const primitives: DisconnectionPolicyPrimitives = {
        holdSeatForMs: 3600000, // 1 hour
      };
      
      const policy = DisconnectionPolicy.fromPrimitives(primitives);
      
      expect(policy.holdSeatForMs).toBe(3600000);
    });
  });

  describe('fromPrimitives - invalid cases', () => {
    it('should reject negative holdSeatForMs', () => {
      const primitives: DisconnectionPolicyPrimitives = {
        holdSeatForMs: -1,
      };
      
      // NonNegativeInt validation will throw before our custom validation
      expect(() => DisconnectionPolicy.fromPrimitives(primitives)).toThrow();
    });

    it('should reject non-integer holdSeatForMs', () => {
      const primitives: DisconnectionPolicyPrimitives = {
        holdSeatForMs: 1.5,
      };
      
      // NonNegativeInt validation will throw
      expect(() => DisconnectionPolicy.fromPrimitives(primitives)).toThrow();
    });
  });

  describe('toPrimitives', () => {
    it('should serialize correctly', () => {
      const primitives: DisconnectionPolicyPrimitives = {
        holdSeatForMs: 30000,
      };
      
      const policy = DisconnectionPolicy.fromPrimitives(primitives);
      
      expect(policy.toPrimitives()).toEqual(primitives);
    });
  });

  describe('roundtrip', () => {
    it('should maintain equality after roundtrip', () => {
      const original: DisconnectionPolicyPrimitives = {
        holdSeatForMs: 120000,
      };
      
      const policy = DisconnectionPolicy.fromPrimitives(original);
      const roundtrip = policy.toPrimitives();
      
      expect(roundtrip).toEqual(original);
    });
  });

  describe('getters', () => {
    it('should provide access to holdSeatForMs', () => {
      const policy = DisconnectionPolicy.fromPrimitives({
        holdSeatForMs: 60000,
      });
      
      expect(policy.holdSeatForMs).toBe(60000);
    });
  });
});
