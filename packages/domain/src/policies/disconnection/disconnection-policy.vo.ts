import { NonNegativeInt } from '../../primitives/non-negative-int/non-negative-int.primitive';
import { DisconnectionPolicyPrimitives } from './types/disconnection.policy';



export class DisconnectionPolicy {
  private constructor(private readonly _holdSeatForMs: number) {}

  static fromPrimitives(raw: DisconnectionPolicyPrimitives): DisconnectionPolicy {
    const holdSeatForMs = NonNegativeInt.fromPrimitives(raw.holdSeatForMs).toPrimitives();

    return new DisconnectionPolicy(holdSeatForMs);
  }

  toPrimitives(): DisconnectionPolicyPrimitives {
    return {
      holdSeatForMs: this._holdSeatForMs,
    };
  }

  get holdSeatForMs(): number {
    return this._holdSeatForMs;
  }
}
