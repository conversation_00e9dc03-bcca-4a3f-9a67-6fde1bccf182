import { LateJoinPolicy } from '../late-join-policy.vo';
import { 
  LateJoinPolicyInvalidError,
  ERR_POLICY_LATE_JOIN_INVALID
} from '../late-join-policy.errors';
import { LateJoinAllocationMode } from '../types/late-join-allocation-mode.enum';
import { LateJoinPolicyPrimitives } from '../types/late-join.policy';

describe('LateJoinPolicy', () => {
  describe('fromPrimitives - valid cases', () => {
    it('should accept mode=BEST_FIT', () => {
      const primitives: LateJoinPolicyPrimitives = {
        allocationMode: LateJoinAllocationMode.BEST_FIT
      };
      
      const policy = LateJoinPolicy.fromPrimitives(primitives);
      
      expect(policy.allocationMode).toBe(LateJoinAllocationMode.BEST_FIT);
    });

    it('should accept mode=NEW_ROOM', () => {
      const primitives: LateJoinPolicyPrimitives = {
        allocationMode: LateJoinAllocationMode.NEW_ROOM
      };
      
      const policy = LateJoinPolicy.fromPrimitives(primitives);
      
      expect(policy.allocationMode).toBe(LateJoinAllocationMode.NEW_ROOM);
    });

    it('should accept mode=LEAST_RECENT', () => {
      const primitives: LateJoinPolicyPrimitives = {
        allocationMode: LateJoinAllocationMode.LEAST_RECENT
      };
      
      const policy = LateJoinPolicy.fromPrimitives(primitives);
      
      expect(policy.allocationMode).toBe(LateJoinAllocationMode.LEAST_RECENT);
    });
  });

  describe('fromPrimitives - invalid cases', () => {
    it('should reject invalid mode', () => {
      const primitives = {
        allocationMode: 'invalidMode' as LateJoinAllocationMode
      };
      
      expect(() => LateJoinPolicy.fromPrimitives(primitives)).toThrow(LateJoinPolicyInvalidError);
      
      try {
        LateJoinPolicy.fromPrimitives(primitives);
      } catch (error) {
        expect(error).toBeInstanceOf(LateJoinPolicyInvalidError);
        expect((error as LateJoinPolicyInvalidError).code).toBe(ERR_POLICY_LATE_JOIN_INVALID);
        expect((error as LateJoinPolicyInvalidError).ctx?.mode).toBe('invalidMode');
        expect((error as LateJoinPolicyInvalidError).ctx?.reason).toBe('mode must be BEST_FIT, NEW_ROOM, or LEAST_RECENT');
      }
    });

    it('should reject empty string mode', () => {
      const primitives = {
        allocationMode: '' as LateJoinAllocationMode
      };
      
      expect(() => LateJoinPolicy.fromPrimitives(primitives)).toThrow(LateJoinPolicyInvalidError);
    });

    it('should reject null mode', () => {
      const primitives = {
        allocationMode: null as any as LateJoinAllocationMode
      };
      
      expect(() => LateJoinPolicy.fromPrimitives(primitives)).toThrow(LateJoinPolicyInvalidError);
    });
  });

  describe('toPrimitives', () => {
    it('should serialize correctly', () => {
      const primitives: LateJoinPolicyPrimitives = {
        allocationMode: LateJoinAllocationMode.BEST_FIT
      };
      
      const policy = LateJoinPolicy.fromPrimitives(primitives);
      
      expect(policy.toPrimitives()).toEqual(primitives);
    });
  });

  describe('roundtrip', () => {
    it('should maintain equality after roundtrip for all valid modes', () => {
      const modes: LateJoinAllocationMode[] = [LateJoinAllocationMode.BEST_FIT, LateJoinAllocationMode.NEW_ROOM, LateJoinAllocationMode.LEAST_RECENT];
      
      for (const mode of modes) {
        const original: LateJoinPolicyPrimitives = { allocationMode: mode };
        const policy = LateJoinPolicy.fromPrimitives(original);
        const roundtrip = policy.toPrimitives();
        
        expect(roundtrip).toEqual(original);
      }
    });
  });

  describe('getters', () => {
    it('should provide access to mode', () => {
      const policy = LateJoinPolicy.fromPrimitives({
        allocationMode: LateJoinAllocationMode.LEAST_RECENT
      });
      
      expect(policy.allocationMode).toBe(LateJoinAllocationMode.LEAST_RECENT);
    });
  });
});
