import { ensure } from '../../support/ensure';
import { LateJoinPolicyInvalidError } from './late-join-policy.errors';
import { LateJoinAllocationMode } from './types/late-join-allocation-mode.enum';
import { LateJoinPolicyPrimitives } from './types/late-join.policy';



export class LateJoinPolicy {
  private constructor(private readonly _allocationMode: LateJoinAllocationMode) {}

  static fromPrimitives(raw: LateJoinPolicyPrimitives): LateJoinPolicy {
    // Validate that mode matches allowed set
    ensure(
      raw.allocationMode === LateJoinAllocationMode.BEST_FIT ||
        raw.allocationMode === LateJoinAllocationMode.NEW_ROOM ||
        raw.allocationMode === LateJoinAllocationMode.LEAST_RECENT,
      new LateJoinPolicyInvalidError({
        mode: raw.allocationMode,
        reason: 'mode must be BEST_FIT, NEW_ROOM, or LEAST_RECENT',
      }),
    );

    return new LateJoinPolicy(raw.allocationMode);
  }

  toPrimitives(): LateJoinPolicyPrimitives {
    return {
      allocationMode: this._allocationMode,
    };
  }

  get allocationMode(): LateJoinAllocationMode {
    return this._allocationMode;
  }
}
