import { Duration } from '../duration.primitive';
import { InvalidDurationError, ERR_PRIMITIVE_DURATION_INVALID } from '../duration.errors';

describe('Duration', () => {
  describe('fromPrimitives_accepts_non_negative_integers', () => {
    const validValues = [
      0,        // Zero duration
      1000,     // 1 second
      60000,    // 1 minute
      3600000,  // 1 hour
      86400000, // 1 day
      500,      // 500ms
    ];

    validValues.forEach(value => {
      it(`accepts valid duration: ${value}ms`, () => {
        const result = Duration.fromPrimitives(value);
        expect(result).toBeInstanceOf(Duration);
        expect(result.toPrimitives()).toBe(value);
      });
    });
  });

  describe('fromPrimitives_rejects_invalid_values', () => {
    const invalidValues = [
      // Negative numbers
      -1,
      -1000,
      -0.1,
      
      // Non-integers
      0.1,
      1.5,
      1000.5,
      
      // Special numbers
      NaN,
      Infinity,
      -Infinity,
      
      // Non-numbers
      '1000' as any,
      '0' as any,
      null as any,
      undefined as any,
      {} as any,
      [] as any,
      true as any,
      false as any,
    ];

    invalidValues.forEach(value => {
      it(`rejects invalid value: ${JSON.stringify(value)}`, () => {
        expect(() => Duration.fromPrimitives(value)).toThrow(InvalidDurationError);
        expect(() => Duration.fromPrimitives(value)).toThrow('Duration must be a non-negative integer (ms).');
        
        try {
          Duration.fromPrimitives(value);
        } catch (error) {
          expect(error).toBeInstanceOf(InvalidDurationError);
          expect((error as InvalidDurationError).code).toBe(ERR_PRIMITIVE_DURATION_INVALID);
          expect((error as InvalidDurationError).ctx?.value).toBe(value);
        }
      });
    });
  });

  describe('toPrimitives_roundtrip', () => {
    it('roundtrip returns original value', () => {
      const validValues = [0, 1000, 60000, 3600000];
      
      validValues.forEach(original => {
        const duration = Duration.fromPrimitives(original);
        expect(duration.toPrimitives()).toBe(original);
      });
    });
  });
});
