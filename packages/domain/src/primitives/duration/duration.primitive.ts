import { ensure } from '../../support/ensure';
import { InvalidDurationError } from './duration.errors';

export class Duration {
  private constructor(private readonly value: number) {}

  static fromPrimitives(raw: number): Duration {
    ensure(
      Number.isInteger(raw) && raw >= 0,
      new InvalidDurationError({ value: raw })
    );
    return new Duration(raw);
  }

  toPrimitives(): number {
    return this.value;
  }
}
