import { ensure } from '../../support/ensure';
import { InvalidUuidError } from './uuid.errors';

// RFC4122 UUID v4 regex pattern
const UUID_V4_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

export class Uuid {
  private constructor(private readonly value: string) {}

  static fromPrimitives(raw: string): Uuid {
    ensure(
      typeof raw === 'string' && UUID_V4_REGEX.test(raw),
      new InvalidUuidError({ value: raw })
    );
    return new Uuid(raw);
  }

  toPrimitives(): string {
    return this.value;
  }

  toString(): string {
    return this.value;
  }

  static generate(): string {
    return crypto.randomUUID();
  }
}
