import { Uuid } from '../uuid.primitive';
import { InvalidUuidError, ERR_PRIMITIVE_UUID_INVALID } from '../uuid.errors';

describe('Uuid', () => {
  describe('fromPrimitives_accepts_valid_uuid_v4', () => {
    const validUuids = [
      '550e8400-e29b-41d4-a716-************',
      'f47ac10b-58cc-4372-a567-0e02b2c3d479',
      '6ba7b810-9dad-41d1-80b4-00c04fd430c8',
      '6ba7b811-9dad-41d1-80b4-00c04fd430c8'
    ];

    validUuids.forEach(uuid => {
      it(`accepts valid UUID: ${uuid}`, () => {
        const result = Uuid.fromPrimitives(uuid);
        expect(result).toBeInstanceOf(Uuid);
        expect(result.toPrimitives()).toBe(uuid);
        expect(result.toString()).toBe(uuid);
      });
    });
  });

  describe('fromPrimitives_rejects_invalid_values', () => {
    const invalidValues = [
      // Empty and null values
      '',
      null as any,
      undefined as any,
      
      // Non-string values
      123 as any,
      {} as any,
      [] as any,
      
      // Malformed UUIDs
      '550e8400-e29b-41d4-a716-44665544000',  // too short
      '550e8400-e29b-41d4-a716-************0', // too long
      '550e8400-e29b-41d4-a716-44665544000g',  // invalid character
      '550e8400-e29b-41d4-a716-************-', // extra dash
      '550e8400e29b41d4a716************',      // no dashes
      
      // Wrong version (not v4)
      '550e8400-e29b-21d4-a716-************',  // version 2
      '550e8400-e29b-31d4-a716-************',  // version 3
      '550e8400-e29b-51d4-a716-************',  // version 5
      
      // Wrong variant
      '550e8400-e29b-41d4-1716-************',  // variant bits wrong
      '550e8400-e29b-41d4-f716-************',  // variant bits wrong
    ];

    invalidValues.forEach(value => {
      it(`rejects invalid value: ${JSON.stringify(value)}`, () => {
        expect(() => Uuid.fromPrimitives(value)).toThrow(InvalidUuidError);
        expect(() => Uuid.fromPrimitives(value)).toThrow('The value is not a valid UUID v4.');
        
        try {
          Uuid.fromPrimitives(value);
        } catch (error) {
          expect(error).toBeInstanceOf(InvalidUuidError);
          expect((error as InvalidUuidError).code).toBe(ERR_PRIMITIVE_UUID_INVALID);
          expect((error as InvalidUuidError).ctx?.value).toBe(value);
        }
      });
    });
  });

  describe('toPrimitives_roundtrip', () => {
    it('roundtrip returns original value', () => {
      const original = '550e8400-e29b-41d4-a716-************';
      const uuid = Uuid.fromPrimitives(original);
      expect(uuid.toPrimitives()).toBe(original);
    });

    it('case insensitive input normalized to lowercase', () => {
      const upperCase = '550E8400-E29B-41D4-A716-************';
      const uuid = Uuid.fromPrimitives(upperCase);
      // The regex is case insensitive, but we store the original case
      expect(uuid.toPrimitives()).toBe(upperCase);
    });
  });
});
