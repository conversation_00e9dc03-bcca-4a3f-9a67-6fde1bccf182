import { PositiveInt } from '../positive-int.primitive';
import { InvalidPositiveIntError, ERR_PRIMITIVE_POSITIVE_INT_INVALID } from '../positive-int.errors';

describe('PositiveInt', () => {
  describe('fromPrimitives_accepts_integers_greater_than_zero', () => {
    const validValues = [1, 2, 9999, 42, 100];

    validValues.forEach(value => {
      it(`accepts valid positive integer: ${value}`, () => {
        const result = PositiveInt.fromPrimitives(value);
        expect(result).toBeInstanceOf(PositiveInt);
        expect(result.toPrimitives()).toBe(value);
      });
    });
  });

  describe('fromPrimitives_rejects_zero_negative_or_non_integer', () => {
    const invalidValues = [
      // Zero and negative numbers
      0,
      -1,
      -42,
      -0.1,
      
      // Non-integers
      1.5,
      2.9,
      0.1,
      
      // Special numbers
      NaN,
      Infinity,
      -Infinity,
      
      // Non-numbers
      '3' as any,
      '1' as any,
      null as any,
      undefined as any,
      {} as any,
      [] as any,
      true as any,
      false as any,
    ];

    invalidValues.forEach(value => {
      it(`rejects invalid value: ${JSON.stringify(value)}`, () => {
        expect(() => PositiveInt.fromPrimitives(value)).toThrow(InvalidPositiveIntError);
        expect(() => PositiveInt.fromPrimitives(value)).toThrow('Value must be a positive integer (> 0).');
        
        try {
          PositiveInt.fromPrimitives(value);
        } catch (error) {
          expect(error).toBeInstanceOf(InvalidPositiveIntError);
          expect((error as InvalidPositiveIntError).code).toBe(ERR_PRIMITIVE_POSITIVE_INT_INVALID);
          expect((error as InvalidPositiveIntError).ctx?.value).toBe(value);
        }
      });
    });
  });

  describe('toPrimitives_roundtrip', () => {
    it('roundtrip returns original value', () => {
      const validValues = [1, 2, 42, 100, 9999];
      
      validValues.forEach(original => {
        const positiveInt = PositiveInt.fromPrimitives(original);
        expect(positiveInt.toPrimitives()).toBe(original);
      });
    });
  });
});
