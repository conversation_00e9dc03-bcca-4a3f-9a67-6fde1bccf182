import { ensure } from '../../support/ensure';
import { InvalidPositiveIntError } from './positive-int.errors';

export class PositiveInt {
  private constructor(private readonly value: number) {}

  static fromPrimitives(raw: number): PositiveInt {
    ensure(
      Number.isInteger(raw) && raw > 0,
      new InvalidPositiveIntError({ value: raw })
    );
    return new PositiveInt(raw);
  }

  toPrimitives(): number {
    return this.value;
  }
}
