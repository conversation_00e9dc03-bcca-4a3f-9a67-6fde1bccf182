import { Instant } from '../instant.primitive';
import { InvalidInstantError, ERR_PRIMITIVE_INSTANT_INVALID } from '../instant.errors';

describe('Instant', () => {
  describe('fromPrimitives_accepts_valid_timestamps', () => {
    const validValues = [
      0,
      1000,
      Date.now(),
      Math.floor(Date.now()),
      1609459200000, // 2021-01-01 00:00:00 UTC
      1640995200000, // 2022-01-01 00:00:00 UTC
    ];

    validValues.forEach(value => {
      it(`accepts valid timestamp: ${value}`, () => {
        const result = Instant.fromPrimitives(value);
        expect(result).toBeInstanceOf(Instant);
        expect(result.toPrimitives()).toBe(value);
      });
    });
  });

  describe('fromPrimitives_rejects_invalid_values', () => {
    const invalidValues = [
      // Negative numbers
      -1,
      -1000,
      
      // Non-integers
      1.5,
      1000.1,
      0.5,
      
      // Special numbers
      NaN,
      Infinity,
      -Infinity,
      
      // Non-numbers
      '123' as any,
      '1000' as any,
      null as any,
      undefined as any,
      {} as any,
      [] as any,
      true as any,
      false as any,
    ];

    invalidValues.forEach(value => {
      it(`rejects invalid value: ${JSON.stringify(value)}`, () => {
        expect(() => Instant.fromPrimitives(value)).toThrow(InvalidInstantError);
        expect(() => Instant.fromPrimitives(value)).toThrow('Value must be a finite non-negative integer (ms since epoch).');
        
        try {
          Instant.fromPrimitives(value);
        } catch (error) {
          expect(error).toBeInstanceOf(InvalidInstantError);
          expect((error as InvalidInstantError).code).toBe(ERR_PRIMITIVE_INSTANT_INVALID);
          expect((error as InvalidInstantError).ctx?.value).toBe(value);
        }
      });
    });
  });

  describe('toPrimitives_roundtrip', () => {
    it('roundtrip returns original value', () => {
      const validValues = [0, 1000, 1609459200000];
      
      validValues.forEach(original => {
        const instant = Instant.fromPrimitives(original);
        expect(instant.toPrimitives()).toBe(original);
      });
    });

    it('accepts Date.now() coerced to integer', () => {
      const now = Math.floor(Date.now());
      const instant = Instant.fromPrimitives(now);
      expect(instant.toPrimitives()).toBe(now);
    });
  });
});
