import { NonNegativeInt } from '../non-negative-int.primitive';
import { InvalidNonNegativeIntError, ERR_PRIMITIVE_NON_NEGATIVE_INT_INVALID } from '../non-negative-int.errors';

describe('NonNegativeInt', () => {
  describe('fromPrimitives_accepts_non_negative_integers', () => {
    const validValues = [0, 1, 2, 42, 100, 9999];

    validValues.forEach(value => {
      it(`accepts valid non-negative integer: ${value}`, () => {
        const result = NonNegativeInt.fromPrimitives(value);
        expect(result).toBeInstanceOf(NonNegativeInt);
        expect(result.toPrimitives()).toBe(value);
      });
    });
  });

  describe('fromPrimitives_rejects_invalid_values', () => {
    const invalidValues = [
      // Negative numbers
      -1,
      -42,
      -0.1,
      
      // Non-integers
      1.1,
      1.5,
      2.9,
      0.1,
      
      // Special numbers
      NaN,
      <PERSON>,
      -<PERSON>,
      
      // Non-numbers
      '0' as any,
      '1' as any,
      null as any,
      undefined as any,
      {} as any,
      [] as any,
      true as any,
      false as any,
    ];

    invalidValues.forEach(value => {
      it(`rejects invalid value: ${JSON.stringify(value)}`, () => {
        expect(() => NonNegativeInt.fromPrimitives(value)).toThrow(InvalidNonNegativeIntError);
        expect(() => NonNegativeInt.fromPrimitives(value)).toThrow('Value must be a non-negative integer (≥ 0).');
        
        try {
          NonNegativeInt.fromPrimitives(value);
        } catch (error) {
          expect(error).toBeInstanceOf(InvalidNonNegativeIntError);
          expect((error as InvalidNonNegativeIntError).code).toBe(ERR_PRIMITIVE_NON_NEGATIVE_INT_INVALID);
          expect((error as InvalidNonNegativeIntError).ctx?.value).toBe(value);
        }
      });
    });
  });

  describe('toPrimitives_roundtrip', () => {
    it('roundtrip returns original value', () => {
      const validValues = [0, 1, 42, 100];
      
      validValues.forEach(original => {
        const nonNegativeInt = NonNegativeInt.fromPrimitives(original);
        expect(nonNegativeInt.toPrimitives()).toBe(original);
      });
    });
  });
});
