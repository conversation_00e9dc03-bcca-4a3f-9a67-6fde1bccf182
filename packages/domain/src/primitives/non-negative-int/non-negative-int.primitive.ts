import { ensure } from '../../support/ensure';
import { InvalidNonNegativeIntError } from './non-negative-int.errors';

export class NonNegativeInt {
  private constructor(private readonly value: number) {}

  static fromPrimitives(raw: number): NonNegativeInt {
    ensure(
      Number.isInteger(raw) && raw >= 0,
      new InvalidNonNegativeIntError({ value: raw })
    );
    return new NonNegativeInt(raw);
  }

  toPrimitives(): number {
    return this.value;
  }
}
