import { ensure } from '../../support/ensure';
import { InvalidNonEmptyStringError } from './non-empty-string.errors';

export class NonEmptyString {
  private constructor(private readonly value: string) {}

  static fromPrimitives(raw: string): NonEmptyString {
    ensure(
      typeof raw === 'string' && raw.trim().length > 0,
      new InvalidNonEmptyStringError({ value: raw })
    );
    return new NonEmptyString(raw.trim());
  }

  toPrimitives(): string {
    return this.value;
  }
}
