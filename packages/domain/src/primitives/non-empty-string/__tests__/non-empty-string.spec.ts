import { NonEmptyString } from '../non-empty-string.primitive';
import { InvalidNonEmptyStringError, ERR_PRIMITIVE_NON_EMPTY_STRING_INVALID } from '../non-empty-string.errors';

describe('NonEmptyString', () => {
  describe('fromPrimitives_accepts_non_empty_strings', () => {
    const validCases = [
      { input: 'Main', expected: 'Main' },
      { input: '  Alice  ', expected: 'Alice' }, // Trimmed
      { input: 'x', expected: 'x' },
      { input: 'Hello World', expected: 'Hello World' },
      { input: '  Test  ', expected: 'Test' },
      { input: '\t\nContent\t\n', expected: 'Content' },
    ];

    validCases.forEach(({ input, expected }) => {
      it(`accepts "${input}" and returns trimmed "${expected}"`, () => {
        const result = NonEmptyString.fromPrimitives(input);
        expect(result).toBeInstanceOf(NonEmptyString);
        expect(result.toPrimitives()).toBe(expected);
      });
    });
  });

  describe('fromPrimitives_rejects_invalid_values', () => {
    const invalidValues = [
      // Empty strings
      '',
      '   ', // Only whitespace
      '\t\n\r ', // Only whitespace characters
      
      // Non-string values
      null as any,
      undefined as any,
      123 as any,
      {} as any,
      [] as any,
      true as any,
      false as any,
    ];

    invalidValues.forEach(value => {
      it(`rejects invalid value: ${JSON.stringify(value)}`, () => {
        expect(() => NonEmptyString.fromPrimitives(value)).toThrow(InvalidNonEmptyStringError);
        expect(() => NonEmptyString.fromPrimitives(value)).toThrow('Value must be a non-empty string.');
        
        try {
          NonEmptyString.fromPrimitives(value);
        } catch (error) {
          expect(error).toBeInstanceOf(InvalidNonEmptyStringError);
          expect((error as InvalidNonEmptyStringError).code).toBe(ERR_PRIMITIVE_NON_EMPTY_STRING_INVALID);
          expect((error as InvalidNonEmptyStringError).ctx?.value).toBe(value);
        }
      });
    });
  });

  describe('toPrimitives_roundtrip', () => {
    it('roundtrip returns trimmed value', () => {
      const testCases = [
        { input: 'Main', expected: 'Main' },
        { input: '  Alice  ', expected: 'Alice' },
        { input: 'x', expected: 'x' },
      ];
      
      testCases.forEach(({ input, expected }) => {
        const nonEmptyString = NonEmptyString.fromPrimitives(input);
        expect(nonEmptyString.toPrimitives()).toBe(expected);
      });
    });

    it('trimming behavior verified', () => {
      const withWhitespace = '  Hello World  ';
      const nonEmptyString = NonEmptyString.fromPrimitives(withWhitespace);
      expect(nonEmptyString.toPrimitives()).toBe('Hello World');
    });
  });
});
