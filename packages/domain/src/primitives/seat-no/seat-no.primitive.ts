import { ensure } from '../../support/ensure';
import { InvalidSeatNoError } from './seat-no.errors';

export class SeatNo {
  private constructor(private readonly value: number) {}

  static fromPrimitives(raw: number): SeatNo {
    ensure(
      Number.isInteger(raw) && raw >= 0,
      new InvalidSeatNoError({ value: raw })
    );
    return new SeatNo(raw);
  }

  toPrimitives(): number {
    return this.value;
  }
}
