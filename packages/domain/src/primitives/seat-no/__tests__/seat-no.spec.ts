import { SeatNo } from '../seat-no.primitive';
import { InvalidSeatNoError, ERR_PRIMITIVE_SEATNO_INVALID } from '../seat-no.errors';

describe('SeatNo', () => {
  describe('fromPrimitives_accepts_non_negative_integers', () => {
    const validValues = [
      0,  // First seat
      1,  // Second seat
      5,  // Sixth seat
      10, // Eleventh seat
      99, // 100th seat
    ];

    validValues.forEach(value => {
      it(`accepts valid seat index: ${value}`, () => {
        const result = SeatNo.fromPrimitives(value);
        expect(result).toBeInstanceOf(SeatNo);
        expect(result.toPrimitives()).toBe(value);
      });
    });
  });

  describe('fromPrimitives_rejects_invalid_values', () => {
    const invalidValues = [
      // Negative numbers
      -1,
      -5,
      -0.1,
      
      // Non-integers
      2.2,
      1.5,
      0.1,
      
      // Special numbers
      NaN,
      Infinity,
      -Infinity,
      
      // Non-numbers
      '0' as any,
      '1' as any,
      null as any,
      undefined as any,
      {} as any,
      [] as any,
      true as any,
      false as any,
    ];

    invalidValues.forEach(value => {
      it(`rejects invalid value: ${JSON.stringify(value)}`, () => {
        expect(() => SeatNo.fromPrimitives(value)).toThrow(InvalidSeatNoError);
        expect(() => SeatNo.fromPrimitives(value)).toThrow('Seat index must be a non-negative integer (0-based).');
        
        try {
          SeatNo.fromPrimitives(value);
        } catch (error) {
          expect(error).toBeInstanceOf(InvalidSeatNoError);
          expect((error as InvalidSeatNoError).code).toBe(ERR_PRIMITIVE_SEATNO_INVALID);
          expect((error as InvalidSeatNoError).ctx?.value).toBe(value);
        }
      });
    });
  });

  describe('toPrimitives_roundtrip', () => {
    it('roundtrip returns original value', () => {
      const validValues = [0, 1, 5, 10, 99];
      
      validValues.forEach(original => {
        const seatNo = SeatNo.fromPrimitives(original);
        expect(seatNo.toPrimitives()).toBe(original);
      });
    });
  });
});
