/**
 * Domain Commands - Public API exports
 * 
 * This module exports all domain command types and utilities.
 * These are payload-only types with no infrastructure concerns.
 */

export type {
  // Base Command Structure
  BaseCommand,

  // Session Lifecycle Commands
  CreateSessionCommand,
  StartSessionCommand,
  PauseSessionCommand,
  ResumeSessionCommand,
  CompleteSessionCommand,
  CancelSessionCommand,

  // Round Management Commands
  AddRoundCommand,
  StartRoundCommand,
  EndCurrentRoundCommand,

  // Participant Management Commands
  AddParticipantCommand,
  JoinParticipantCommand,
  SoftLeaveParticipantCommand,
  HardLeaveParticipantCommand,
  SeatParticipantCommand,

  // Host Management Commands
  SetHostCommand,

  // Room Management Commands
  CreateBreakoutRoomCommand,
  AssignParticipantToRoomCommand,
  ReleaseSeatByParticipantCommand,
  RotateBreakoutsCommand,
  ReleaseAllReservedSeatsCommand,

  // Union and Utility Types
  DomainCommand,
  CommandKind,
  CommandPayload
} from './commands';

export {
  // Type Guards and Utilities
  isDomainCommand,
  isCommandOfKind,
  createCommand
} from './commands';
