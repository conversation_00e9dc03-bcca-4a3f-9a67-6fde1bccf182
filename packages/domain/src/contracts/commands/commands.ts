/**
 * Domain Command Intents - Type definitions for all domain commands in the system
 *
 * These are payload-only types with no infrastructure concerns.
 * Each command has a discriminated union structure with:
 * - kind: string literal for command type identification
 * - version: number for forward compatibility
 * - payload: command-specific data using primitives and value object IDs
 * - expectedVersion?: number for optimistic concurrency control
 */

import { ParticipantRole } from "value-objects/participants/contracts/participant-role.enum";
import { RoundKind } from "value-objects/sessions/round/contracts/round-kind.enum";

// ============================================================================
// Base Command Structure
// ============================================================================

export enum ActorRole {
  SYSTEM = 'SYSTEM',
  HOST = 'HOST',
  CO_HOST = 'CO_HOST',
  ADMIN = 'ADMIN',
  ATTENDEE = 'ATTENDEE',
}

export interface Actor {
  role: ActorRole;
  actorId?: string;
}

export interface BaseCommand {
  readonly kind: string;
  readonly version: number;
  readonly expectedVersion?: number; // For optimistic concurrency control
  readonly commandId: string; // For command deduplication
  readonly correlationId?: string;
  readonly causationId?: string;
  readonly issuedAt: number;
  readonly actor: Actor;
}

// ============================================================================
// Session Lifecycle Commands
// ============================================================================

export interface CreateSessionCommand extends BaseCommand {
  readonly kind: 'CreateSession';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string;
    readonly config: object;
    readonly createdByUserId: string;
    readonly mainRoomId: string;
  };
}

export interface StartSessionCommand extends BaseCommand {
  readonly kind: 'StartSession';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string;
  };
}

export interface PauseSessionCommand extends BaseCommand {
  readonly kind: 'PauseSession';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string;
  };
}

export interface ResumeSessionCommand extends BaseCommand {
  readonly kind: 'ResumeSession';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string;
  };
}

export interface CompleteSessionCommand extends BaseCommand {
  readonly kind: 'CompleteSession';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string;
  };
}

export interface CancelSessionCommand extends BaseCommand {
  readonly kind: 'CancelSession';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string;
  };
}

// ============================================================================
// Round Management Commands
// ============================================================================

export interface AddRoundCommand extends BaseCommand {
  readonly kind: 'AddRound';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string;
    readonly round: {
      readonly roundNo: number;
      readonly kind: RoundKind;
      readonly durationMs: number;
      readonly questions: string[];
    };
  };
}

export interface StartRoundCommand extends BaseCommand {
  readonly kind: 'StartRound';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string;
    readonly roundNo: number;
  };
}

export interface EndCurrentRoundCommand extends BaseCommand {
  readonly kind: 'EndCurrentRound';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string;
  };
}

// ============================================================================
// Participant Management Commands
// ============================================================================

export interface AddParticipantCommand extends BaseCommand {
  readonly kind: 'AddParticipant';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string;
    readonly participantId: string;
    readonly participantRole?: ParticipantRole;
  };
}

export interface JoinParticipantCommand extends BaseCommand {
  readonly kind: 'JoinParticipant';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string;
    readonly participantId: string;
    readonly participantRole?: ParticipantRole;
  };
}

export interface SoftLeaveParticipantCommand extends BaseCommand {
  readonly kind: 'SoftLeaveParticipant';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string;
    readonly participantId: string;
  };
}

export interface HardLeaveParticipantCommand extends BaseCommand {
  readonly kind: 'HardLeaveParticipant';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string;
    readonly participantId: string;
  };
}

export interface SeatParticipantCommand extends BaseCommand {
  readonly kind: 'SeatParticipant';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string;
    readonly participantId: string;
  };
}

export interface RestoreParticipantCommand extends BaseCommand {
  readonly kind: 'RestoreParticipant';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string;
    readonly participantId: string;
  };
}

// ============================================================================
// Host Management Commands
// ============================================================================

export interface SetHostCommand extends BaseCommand {
  readonly kind: 'SetHost';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string;
    readonly hostId: string;
  };
}

// ============================================================================
// Room Management Commands
// ============================================================================

export interface CreateBreakoutRoomCommand extends BaseCommand {
  readonly kind: 'CreateBreakoutRoom';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string;
    readonly roomId: string;
  };
}

export interface AssignParticipantToRoomCommand extends BaseCommand {
  readonly kind: 'AssignParticipantToRoom';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string;
    readonly participantId: string;
    readonly roomId: string;
  };
}

export interface ReleaseSeatByParticipantCommand extends BaseCommand {
  readonly kind: 'ReleaseSeatByParticipant';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string;
    readonly participantId: string;
  };
}

export interface RotateBreakoutsCommand extends BaseCommand {
  readonly kind: 'RotateBreakouts';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string;
  };
}

export interface ReleaseAllReservedSeatsCommand extends BaseCommand {
  readonly kind: 'ReleaseAllReservedSeats';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string;
  };
}

// ============================================================================
// Union Type for All Domain Commands
// ============================================================================

export type DomainCommand =
  // Session Lifecycle
  | CreateSessionCommand
  | StartSessionCommand
  | PauseSessionCommand
  | ResumeSessionCommand
  | CompleteSessionCommand
  | CancelSessionCommand
  // Round Management
  | AddRoundCommand
  | StartRoundCommand
  | EndCurrentRoundCommand
  // Participant Management
  | AddParticipantCommand
  | JoinParticipantCommand
  | SoftLeaveParticipantCommand
  | HardLeaveParticipantCommand
  | SeatParticipantCommand
  | RestoreParticipantCommand
  // Host Management
  | SetHostCommand
  // Room Management
  | CreateBreakoutRoomCommand
  | AssignParticipantToRoomCommand
  | ReleaseSeatByParticipantCommand
  | RotateBreakoutsCommand
  | ReleaseAllReservedSeatsCommand;

// ============================================================================
// Utility Types
// ============================================================================

/**
 * Extract the command kind from a domain command type
 */
export type CommandKind = DomainCommand['kind'];

/**
 * Extract the payload type for a specific command kind
 */
export type CommandPayload<K extends CommandKind> = Extract<
  DomainCommand,
  { kind: K }
>['payload'];

// ============================================================================
// Type Guards and Utilities
// ============================================================================

/**
 * Type guard to check if an object is a valid domain command
 */
export function isDomainCommand(obj: unknown): obj is DomainCommand {
  if (typeof obj !== 'object' || obj === null) {
    return false;
  }

  const candidate = obj as Record<string, unknown>;

  // Check required structure
  if (
    typeof candidate.kind !== 'string' ||
    typeof candidate.version !== 'number' ||
    typeof candidate.payload !== 'object' ||
    candidate.payload === null
  ) {
    return false;
  }

  // Check version is 1 (current version)
  if (candidate.version !== 1) {
    return false;
  }

  // Check if kind is a valid command kind
  const validKinds: CommandKind[] = [
    // Session Lifecycle
    'CreateSession',
    'StartSession',
    'PauseSession',
    'ResumeSession',
    'CompleteSession',
    'CancelSession',
    // Round Management
    'AddRound',
    'StartRound',
    'EndCurrentRound',
    // Participant Management
    'AddParticipant',
    'JoinParticipant',
    'SoftLeaveParticipant',
    'HardLeaveParticipant',
    'SeatParticipant',
    'RestoreParticipant',
    // Host Management
    'SetHost',
    // Room Management
    'CreateBreakoutRoom',
    'AssignParticipantToRoom',
    'ReleaseSeatByParticipant',
    'RotateBreakouts',
    'ReleaseAllReservedSeats',
  ];

  return validKinds.includes(candidate.kind as CommandKind);
}

/**
 * Type guard to check if a command is of a specific kind
 */
export function isCommandOfKind<K extends CommandKind>(
  command: DomainCommand,
  kind: K,
): command is Extract<DomainCommand, { kind: K }> {
  return command.kind === kind;
}

/**
 * Helper function to create a command with proper typing
 */
export function createCommand<K extends CommandKind>(
  kind: K,
  payload: CommandPayload<K>,
  options: {
    expectedVersion: number;
    commandId: string;
    correlationId?: string;
    causationId?: string;
    issuedAt: number;
    actor: Actor;
  },
): Extract<DomainCommand, { kind: K }> {
  return {
    kind,
    version: 1,
    payload,
    ...options,
  } as Extract<DomainCommand, { kind: K }>;
}
