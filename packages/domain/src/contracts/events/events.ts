/**
 * Domain Events - Type definitions for all domain events in the system
 *
 * These are payload-only types with no infrastructure concerns.
 * Each event has a discriminated union structure with:
 * - kind: string literal for event type identification
 * - version: number for forward compatibility
 * - payload: event-specific data using primitives and value object IDs
 * - occurredAt: number for event ordering
 */

// ============================================================================
// Base Event Structure
// ============================================================================

export interface BaseEvent {
  readonly kind: string;
  readonly version: number;
  readonly correlationId?: string;
  readonly causationId?: string;
  readonly occurredAt: number; // Instant primitive
}

// ============================================================================
// Session Lifecycle Events
// ============================================================================

export interface SessionCreatedEvent extends BaseEvent {
  readonly kind: 'SessionCreated';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string;
    readonly createdByUserId: string;
  };
}

export interface SessionStartedEvent extends BaseEvent {
  readonly kind: 'SessionStarted';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string;
  };
}

export interface SessionPausedEvent extends BaseEvent {
  readonly kind: 'SessionPaused';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; 
  };
}

export interface SessionResumedEvent extends BaseEvent {
  readonly kind: 'SessionResumed';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; 
  };
}

export interface SessionCompletedEvent extends BaseEvent {
  readonly kind: 'SessionCompleted';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; 
  };
}

export interface SessionCanceledEvent extends BaseEvent {
  readonly kind: 'SessionCanceled';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; 
  };
}

// ============================================================================
// Round Management Events
// ============================================================================

export interface RoundAddedToSessionEvent extends BaseEvent {
  readonly kind: 'RoundAddedToSession';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; 
    readonly roundNo: number; 
    readonly round: {
      readonly roundNo: number; 
      readonly kind: string; 
      readonly durationMs: number; 
      readonly questions: readonly string[]; 
    };
  };
}

export interface RoundStartedEvent extends BaseEvent {
  readonly kind: 'RoundStarted';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; 
    readonly roundNo: number; 
  };
}

export interface RoundEndedEvent extends BaseEvent {
  readonly kind: 'RoundEnded';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; 
    readonly roundNo: number; 
  };
}

// ============================================================================
// Participant Management Events
// ============================================================================

export interface ParticipantAddedToSessionEvent extends BaseEvent {
  readonly kind: 'ParticipantAddedToSession';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; 
    readonly participantId: string; 
    readonly participantRole: string; 
  };
}

export interface ParticipantJoinedEvent extends BaseEvent {
  readonly kind: 'ParticipantJoined';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; 
    readonly participantId: string; 
    readonly participantRole: string; 
  };
}

export interface ParticipantSoftLeftEvent extends BaseEvent {
  readonly kind: 'ParticipantSoftLeft';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; 
    readonly participantId: string; 
  };
}

export interface ParticipantHardLeftEvent extends BaseEvent {
  readonly kind: 'ParticipantHardLeft';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; 
    readonly participantId: string; 
  };
}

export interface ParticipantSeatedEvent extends BaseEvent {
  readonly kind: 'ParticipantSeated';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; 
    readonly participantId: string; 
    readonly roomId: string; 
  };
}

export interface ParticipantRestoredEvent extends BaseEvent {
  readonly kind: 'ParticipantRestored';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; 
    readonly participantId: string; 
    readonly roomId: string; 
  };
}

// ============================================================================
// Host Management Events
// ============================================================================

export interface HostSetEvent extends BaseEvent {
  readonly kind: 'HostSet';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; 
    readonly hostId: string; 
  };
}

// ============================================================================
// Room Management Events
// ============================================================================

export interface BreakoutRoomCreatedEvent extends BaseEvent {
  readonly kind: 'BreakoutRoomCreated';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; 
    readonly roomId: string; 
  };
}

export interface ParticipantAssignedToRoomEvent extends BaseEvent {
  readonly kind: 'ParticipantAssignedToRoom';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; 
    readonly participantId: string; 
    readonly fromRoomId: string; 
    readonly toRoomId: string; 
  };
}

export interface SeatReleasedByParticipantEvent extends BaseEvent {
  readonly kind: 'SeatReleasedByParticipant';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; 
    readonly participantId: string; 
  };
}

export interface BreakoutRoomsRotatedEvent extends BaseEvent {
  readonly kind: 'BreakoutRoomsRotated';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; 
    readonly moves: ReadonlyArray<{
      readonly participantId: string; 
      readonly fromRoomId: string; 
      readonly toRoomId: string; 
    }>;
  };
}

export interface AllReservedSeatsReleasedEvent extends BaseEvent {
  readonly kind: 'AllReservedSeatsReleased';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; 
  };
}

export interface ParticipantsDistributedEvent extends BaseEvent {
  readonly kind: 'ParticipantsDistributed';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; 
    readonly roundNo: number; 
  };
}

export interface ParticipantsReturnedToMainEvent extends BaseEvent {
  readonly kind: 'ParticipantsReturnedToMain';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; 
  };
}

// ============================================================================
// Union Type for All Domain Events
// ============================================================================

export type DomainEvent =
  // Session Lifecycle
  | SessionCreatedEvent
  | SessionStartedEvent
  | SessionPausedEvent
  | SessionResumedEvent
  | SessionCompletedEvent
  | SessionCanceledEvent
  // Round Management
  | RoundAddedToSessionEvent
  | RoundStartedEvent
  | RoundEndedEvent
  // Participant Management
  | ParticipantAddedToSessionEvent
  | ParticipantJoinedEvent
  | ParticipantSoftLeftEvent
  | ParticipantHardLeftEvent
  | ParticipantSeatedEvent
  | ParticipantRestoredEvent
  // Host Management
  | HostSetEvent
  // Room Management
  | BreakoutRoomCreatedEvent
  | ParticipantAssignedToRoomEvent
  | SeatReleasedByParticipantEvent
  | BreakoutRoomsRotatedEvent
  | AllReservedSeatsReleasedEvent
  | ParticipantsDistributedEvent
  | ParticipantsReturnedToMainEvent;

// ============================================================================
// Type Utilities
// ============================================================================

/**
 * Extract event kind from a domain event type
 */
export type EventKind = DomainEvent['kind'];

/**
 * Extract payload type for a specific event kind
 */
export type EventPayload<K extends EventKind> = Extract<
  DomainEvent,
  { kind: K }
>['payload'];

/**
 * Type guard to check if an object is a valid domain event
 */
export function isDomainEvent(obj: unknown): obj is DomainEvent {
  if (typeof obj !== 'object' || obj === null) {
    return false;
  }

  const event = obj as Record<string, unknown>;

  return (
    typeof event.kind === 'string' &&
    event.version === 1 &&
    typeof event.occurredAt === 'number' &&
    typeof event.payload === 'object' &&
    event.payload !== null
  );
}

/**
 * Type guard for specific event kinds
 */
export function isEventOfKind<K extends EventKind>(
  event: DomainEvent,
  kind: K,
): event is Extract<DomainEvent, { kind: K }> {
  return event.kind === kind;
}

/**
 * Helper function to create an event with proper typing
 */
export function createEvent<K extends EventKind>(
  kind: K,
  payload: EventPayload<K>,
  metadata: {
    correlationId: string;
    causationId: string;
    occurredAt: number;
  },
): Extract<DomainEvent, { kind: K }> {
  return {
    kind,
    version: 1,
    correlationId: metadata.correlationId,
    causationId: metadata.causationId,
    occurredAt: metadata.occurredAt,
    payload,
  } as Extract<DomainEvent, { kind: K }>;
}
