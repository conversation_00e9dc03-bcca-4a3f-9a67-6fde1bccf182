/**
 * Domain Events - Public API exports
 * 
 * This module exports all domain event types and utilities.
 * These are payload-only types with no infrastructure concerns.
 */

export type {
  // Base Event Structure
  BaseEvent,

  // Session Lifecycle Events
  SessionCreatedEvent,
  SessionStartedEvent,
  SessionPausedEvent,
  SessionResumedEvent,
  SessionCompletedEvent,
  SessionCanceledEvent,

  // Round Management Events
  RoundAddedToSessionEvent,
  RoundStartedEvent,
  RoundEndedEvent,

  // Participant Management Events
  ParticipantAddedToSessionEvent,
  ParticipantJoinedEvent,
  ParticipantSoftLeftEvent,
  ParticipantHardLeftEvent,
  ParticipantSeatedEvent,

  // Host Management Events
  HostSetEvent,

  // Room Management Events
  BreakoutRoomCreatedEvent,
  ParticipantAssignedToRoomEvent,
  SeatReleasedByParticipantEvent,
  BreakoutRoomsRotatedEvent,
  AllReservedSeatsReleasedEvent,

  // Union and Utility Types
  DomainEvent,
  EventKind,
  EventPayload,
} from './events';

export {
  // Type Guards and Utilities
  isDomainEvent,
  isEventOfKind,
  createEvent,
} from './events';

// Testing utilities
export * from './testing';
