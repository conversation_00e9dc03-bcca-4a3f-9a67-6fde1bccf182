import { ensure } from '../../support/ensure';
import { SeatNo } from '../../primitives/seat-no/seat-no.primitive';
import { Instant } from '../../primitives/instant/instant.primitive';
import { PersistenceMappingError } from '../../errors/persistence-mapping-error';
import { DisconnectionPolicy } from '../../policies/disconnection/disconnection-policy.vo';
import { InvalidSeatError, SeatStateError } from './seat.errors';
import { SeatState } from './contracts/seat-state.enum';
import { SeatPrimitives, SeatPrimitivesWithState } from './contracts/seat.type';
import { ParticipantId } from '../../value-objects/participants/participant-id/participant-id.vo';

export class Seat {
  private readonly state: SeatState;
  private constructor(
    public readonly seatNo: SeatNo,
    private participantId?: ParticipantId,
    private reservedSince?: Instant,
    private assignedAt?: Instant,
    private releasedAt?: Instant,
  ) {
    // Compute state from properties
    let state: SeatState;

    if (
      this.reservedSince &&
      this.participantId &&
      this.assignedAt
    ) {
      state = SeatState.RESERVED_FOR_RECONNECT;
    } else if (this.participantId && this.assignedAt && !this.reservedSince) {
      state = SeatState.OCCUPIED;
    } else if (!this.participantId) {
      state = SeatState.EMPTY;
    }

    this.state = state;
  }

  static fromPrimitives(dto: SeatPrimitives): Seat {
    try {
      ensure(dto, new PersistenceMappingError('Seat DTO is null or undefined'));

      const seatNo = SeatNo.fromPrimitives(dto.seatNo);

      const participantId = dto.participantId
        ? ParticipantId.fromPrimitives(dto.participantId)
        : undefined;
      const reservedSince = dto.reservedSince
        ? Instant.fromPrimitives(dto.reservedSince)
        : undefined;
      const assignedAt = dto.assignedAt
        ? Instant.fromPrimitives(dto.assignedAt)
        : undefined;
      const releasedAt = dto.releasedAt
        ? Instant.fromPrimitives(dto.releasedAt)
        : undefined;

      return new Seat(
        seatNo,
        participantId,
        reservedSince,
        assignedAt,
        releasedAt,
      );
    } catch (error) {
      if (error instanceof PersistenceMappingError) {
        throw error;
      }
      throw new PersistenceMappingError('Failed to map seat from primitives', {
        originalError: error,
      });
    }
  }

  toPrimitives(): SeatPrimitivesWithState {
    return {
      seatNo: this.seatNo.toPrimitives(),
      state: this.state,
      participantId: this.participantId?.toPrimitives(),
      reservedSince: this.reservedSince?.toPrimitives(),
      assignedAt: this.assignedAt?.toPrimitives(),
      releasedAt: this.releasedAt?.toPrimitives(),
    };
  }

  assign(participantId: ParticipantId, at: Instant): Seat {
    ensure(
      this.isEmpty,
      new SeatStateError('Cannot assign participant to non-empty seat', {
        currentState: this.state,
        seatNo: this.seatNo.toPrimitives(),
      }),
    );

    return new Seat(
      this.seatNo,
      participantId,
      undefined, // Clear any previous reservation
      at,
      undefined, // Clear any previous release time
    );
  }

  release(at: Instant): Seat {
    return new Seat(
      this.seatNo,
      undefined, // Clear participant
      undefined, // Clear reservation
      this.assignedAt, // Preserve assignment time
      at,
    );
  }

  reserveForReconnect(disconnectedAt: Instant): Seat {
    ensure(
      this.isOccupied,
      new SeatStateError(
        'Cannot reserve seat for reconnect unless currently occupied',
        {
          currentState: this.state,
          seatNo: this.seatNo.toPrimitives(),
        },
      ),
    );

    ensure(
      this.participantId,
      new InvalidSeatError('Cannot reserve seat without participant ID', {
        seatNo: this.seatNo.toPrimitives(),
      }),
    );

    return new Seat(
      this.seatNo,
      this.participantId,
      disconnectedAt,
      this.assignedAt,
      this.releasedAt,
    );
  }

  restoreAfterReconnect(
    participantId: ParticipantId,
    at: Instant,
    policy: DisconnectionPolicy,
  ): Seat {
    ensure(
      this.isReservedForReconnect,
      new SeatStateError('Cannot restore seat unless reserved for reconnect', {
        currentState: this.state,
        seatNo: this.seatNo.toPrimitives(),
      }),
    );

    ensure(
      this.participantId &&
        this.participantId.toPrimitives() === participantId.toPrimitives(),
      new InvalidSeatError('Participant ID does not match reserved seat', {
        expectedParticipantId: this.participantId?.toPrimitives(),
        providedParticipantId: participantId.toPrimitives(),
        seatNo: this.seatNo.toPrimitives(),
      }),
    );

    ensure(
      this.reservedSince,
      new InvalidSeatError('Seat reserved but no reservation timestamp', {
        seatNo: this.seatNo.toPrimitives(),
      }),
    );

    const reconnectDeadline =
      this.reservedSince.toPrimitives() + policy.holdSeatForMs;
    ensure(
      at.toPrimitives() <= reconnectDeadline,
      new SeatStateError('Reconnection attempt past deadline', {
        reconnectDeadline,
        attemptedAt: at.toPrimitives(),
        seatNo: this.seatNo.toPrimitives(),
      }),
    );

    return new Seat(
      this.seatNo,
      participantId,
      undefined, // Clear reservation
      this.assignedAt, // Preserve assignment time
      this.releasedAt,
    );
  }

  // Getters for state inspection
  get currentState(): SeatState {
    return this.state;
  }

  get currentParticipantId(): string | undefined {
    return this.participantId?.toPrimitives();
  }

  get isOccupied(): boolean {
    return Boolean(
      this.participantId && this.assignedAt && !this.reservedSince,
    );
  }

  get isEmpty(): boolean {
    return Boolean(!this.participantId);
  }

  get isReservedForReconnect(): boolean {
    return Boolean(
      this.reservedSince &&
        this.participantId &&
        this.assignedAt &&
        !this.releasedAt,
    );
  }
}
