import { SeatState } from './seat-state.enum';

export type OccupiedSeat = {
  seatNo: number;
  participantId: string;
  assignedAt: number;
  state: SeatState.OCCUPIED;
};

export type ReservedSeat = {
  seatNo: number;
  participantId: string;
  reservedSince: number;
  assignedAt: number;
  state: SeatState.RESERVED_FOR_RECONNECT;
};

export type EmptySeat = {
  seatNo: number;
  state: SeatState.EMPTY;
  releasedAt?: number;
};

export type SeatPrimitives = {
  seatNo: number;
  participantId?: string;
  reservedSince?: number;
  assignedAt?: number;
  releasedAt?: number;
};

export type SeatPrimitivesWithState = SeatPrimitives & { state: SeatState };
