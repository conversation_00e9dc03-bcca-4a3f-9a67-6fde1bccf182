import { DomainError, type ErrorContext } from '../../errors/domain-error';

export const ERR_ENTITY_SEAT_INVALID = 'ENTITY.SEAT.INVALID' as const;
export const ERR_ENTITY_SEAT_STATE = 'ENTITY.SEAT.STATE' as const;

export class InvalidSeatError extends DomainError {
  constructor(message: string, ctx?: ErrorContext) {
    super(ERR_ENTITY_SEAT_INVALID, message, ctx);
  }
}

export class SeatStateError extends DomainError {
  constructor(message: string, ctx?: ErrorContext) {
    super(ERR_ENTITY_SEAT_STATE, message, ctx);
  }
}
