import { ensure } from '../../support/ensure';
import { Instant } from '../../primitives/instant/instant.primitive';
import { RoomId } from '../../value-objects/rooms/room-id/room-id.vo';
import { PersistenceMappingError } from '../../errors/persistence-mapping-error';
import { PresenceTimeError } from './presence.errors';
import { ParticipantPresencePrimitives } from './contracts/presence.type';
import { ParticipantRole } from '../../value-objects/participants/contracts/participant-role.enum';
import { ParticipantId } from '../../value-objects/participants/participant-id/participant-id.vo';
import { PresenceTags } from './contracts/presence-tags.type';

export class ParticipantPresence {
  private constructor(
    private _participantId: ParticipantId,
    private _tags: PresenceTags,
    private _joinedAt?: Instant,
    private _leftAt?: Instant,
    private _roomId?: RoomId,
  ) {}

  static fromPrimitives(
    dto: ParticipantPresencePrimitives,
  ): ParticipantPresence {
    try {
      ensure(
        dto != null,
        new PersistenceMappingError(
          'ParticipantPresence DTO is null or undefined',
        ),
      );

      ensure(
        dto.tags.role,
        new PersistenceMappingError('Participant role is missing', {
          tags: dto.tags,
        }),
      );

      ensure(
        dto.participantId,
        new PersistenceMappingError('ParticipantId is null or undefined'),
      );

      const participantId = ParticipantId.fromPrimitives(dto.participantId);
      const joinedAt = dto.joinedAt
        ? Instant.fromPrimitives(dto.joinedAt)
        : undefined;
      const leftAt = dto.leftAt
        ? Instant.fromPrimitives(dto.leftAt)
        : undefined;
      const currentRoomId = dto.currentRoomId
        ? RoomId.fromPrimitives(dto.currentRoomId)
        : undefined;

      const tags = { ...dto.tags };

      return new ParticipantPresence(
        participantId,
        tags,
        joinedAt,
        leftAt,
        currentRoomId,
      );
    } catch (error) {
      if (error instanceof PersistenceMappingError) {
        throw error;
      }
      throw new PersistenceMappingError(
        'Failed to map participant presence from primitives',
        { originalError: error },
      );
    }
  }

  static create(
    participantId: ParticipantId,
    role: ParticipantRole,
  ): ParticipantPresence {
    const tags = { role };
    return new ParticipantPresence(
      participantId,
      tags,
      undefined,
      undefined,
      undefined,
    );
  }

  toPrimitives(): ParticipantPresencePrimitives {
    return {
      participantId: this._participantId.toPrimitives(),
      joinedAt: this._joinedAt?.toPrimitives(),
      leftAt: this._leftAt?.toPrimitives(),
      currentRoomId: this._roomId?.toPrimitives(),
      tags: this._tags,
    };
  }

  onJoin(at: Instant): ParticipantPresence {
    // Validate time progression if we have a previous timestamp
    if (this._leftAt) {
      ensure(
        at.toPrimitives() >= this._leftAt.toPrimitives(),
        new PresenceTimeError('Join time cannot be before last leave time', {
          joinTime: at.toPrimitives(),
          lastLeaveTime: this._leftAt.toPrimitives(),
          participantId: this._participantId.toPrimitives(),
        }),
      );
    }

    return new ParticipantPresence(
      this._participantId,
      this._tags,
      this._joinedAt || at, // Set join time on first join, preserve on subsequent joins
      undefined, // Clear leftAt
      this._roomId,
    );
  }

  onSoftLeave(at: Instant): ParticipantPresence {
       // Validate time progression
    if (this._joinedAt) {
      ensure(
        at.toPrimitives() >= this._joinedAt.toPrimitives(),
        new PresenceTimeError('Leave time cannot be before join time', {
          leaveTime: at.toPrimitives(),
          joinTime: this._joinedAt.toPrimitives(),
          participantId: this._participantId.toPrimitives(),
        }),
      );
    }
    return new ParticipantPresence(
      this._participantId,
      this._tags,
      this._joinedAt,
      at,
      this._roomId,
    );
  } 

  onLeave(at: Instant): ParticipantPresence {
    // Validate time progression
    if (this._joinedAt) {
      ensure(
        at.toPrimitives() >= this._joinedAt.toPrimitives(),
        new PresenceTimeError('Leave time cannot be before join time', {
          leaveTime: at.toPrimitives(),
          joinTime: this._joinedAt.toPrimitives(),
          participantId: this._participantId.toPrimitives(),
        }),
      );
    }

    return new ParticipantPresence(
      this._participantId,
      this._tags,
      this._joinedAt,
      at,
      undefined,
    );
  }

  enterRoom(roomId: RoomId, at: Instant): ParticipantPresence {
    // Validate time progression against join time
    if (this._joinedAt) {
      ensure(
        at.toPrimitives() >= this._joinedAt.toPrimitives(),
        new PresenceTimeError('Room enter time cannot be before join time', {
          enterTime: at.toPrimitives(),
          joinTime: this._joinedAt.toPrimitives(),
          participantId: this._participantId.toPrimitives(),
        }),
      );
    }

    return new ParticipantPresence(
      this._participantId,
      this._tags,
      this._joinedAt,
      undefined, // Clear leftAt
      roomId,
    );
  }

  exitRoom(at: Instant): ParticipantPresence {
    // Validate time progression against join time
    if (this._joinedAt) {
      ensure(
        at.toPrimitives() >= this._joinedAt.toPrimitives(),
        new PresenceTimeError('Room exit time cannot be before join time', {
          exitTime: at.toPrimitives(),
          joinTime: this._joinedAt.toPrimitives(),
          participantId: this._participantId.toPrimitives(),
        }),
      );
    }

    return new ParticipantPresence(
      this._participantId,
      this._tags,
      this._joinedAt,
      this._leftAt,
      undefined, // Clear current room
    );
  }

  updateTags(
    tags: Partial<PresenceTags>,
  ): ParticipantPresence {
    const updatedTags = { ...this._tags };
    Object.entries(tags).forEach(([key, value]) => {
      updatedTags[key] = value;
    });

    // Return same instance if tags haven't changed
    if (JSON.stringify(this._tags) === JSON.stringify(updatedTags)) {
      return this;
    }

    return new ParticipantPresence(
      this._participantId,
      updatedTags,
      this._joinedAt,
      this._leftAt,
      this._roomId,
    );
  }

  // Getters for state inspection

  get hasJoined() {
    return this._joinedAt != null;
  }

  get hasLeft() {
    return this._leftAt != null;
  }

  get isInRoom() {
    return this._roomId != null;
  }

  get roomId() {
    return this._roomId?.toPrimitives();
  }

  get participantId() {
    return this._participantId.toPrimitives();
  }

  get joinTime() {
    return this._joinedAt?.toPrimitives();
  }

  get leaveTime() {
    return this._leftAt?.toPrimitives();
  }

  get tags() {
    return this._tags;
  }
}
