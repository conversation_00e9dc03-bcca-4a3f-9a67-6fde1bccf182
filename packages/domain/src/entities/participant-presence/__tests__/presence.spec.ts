/* eslint-disable @typescript-eslint/no-explicit-any */
import { ParticipantPresence } from '../presence.entity';
import {
  PresenceTimeError,
  ERR_ENTITY_PRESENCE_TIME,
} from '../presence.errors';
import { PersistenceMappingError } from '../../../errors/persistence-mapping-error';
import { Instant } from '../../../primitives/instant/instant.primitive';
import { RoomId } from '../../../value-objects/rooms/room-id/room-id.vo';
import { ParticipantPresencePrimitives } from '../contracts/presence.type';
import { ParticipantId } from '../../../value-objects/participants/participant-id/participant-id.vo';
import { ParticipantRole } from '../../../value-objects/participants/contracts/participant-role.enum';

describe('ParticipantPresence', () => {
  const validParticipantId = '550e8400-e29b-41d4-a716-446655440000';
  const validRoomId = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
  const baseTimestamp = 1640995200000; // 2022-01-01 00:00:00 UTC
  const laterTimestamp = 1640995260000; // 1 minute later
  const evenLaterTimestamp = 1640995320000; // 2 minutes later

  const validPresenceDto: ParticipantPresencePrimitives = {
    participantId: validParticipantId,
    joinedAt: baseTimestamp,
    currentRoomId: validRoomId,
    tags: { role: ParticipantRole.MEMBER, englishLevel: 1 },
  };

  describe('fromPrimitives', () => {
    it('creates presence from valid DTO', () => {
      const presence = ParticipantPresence.fromPrimitives(validPresenceDto);

      expect(presence).toBeInstanceOf(ParticipantPresence);
      expect(presence.participantId).toBe(validParticipantId);
      expect(presence.hasJoined).toBe(true);
      expect(presence.hasLeft).toBe(false);
      expect(presence.isInRoom).toBe(true);
      expect(presence.roomId).toBe(validRoomId);
      expect(presence.joinTime).toBe(baseTimestamp);
      expect(presence.tags).toEqual({
        role: ParticipantRole.MEMBER,
        englishLevel: 1,
      });
    });

    it('creates presence without optional fields', () => {
      const minimalDto: ParticipantPresencePrimitives = {
        participantId: validParticipantId,
        tags: { role: ParticipantRole.MEMBER },
      };

      const presence = ParticipantPresence.fromPrimitives(minimalDto);

      expect(presence.hasJoined).toBe(false);
      expect(presence.hasLeft).toBe(false);
      expect(presence.isInRoom).toBe(false);
      expect(presence.roomId).toBeUndefined();
      expect(presence.joinTime).toBeUndefined();
      expect(presence.leaveTime).toBeUndefined();
      expect(presence.tags).toEqual({ role: ParticipantRole.MEMBER });
    });

    it('throws PersistenceMappingError for null DTO', () => {
      expect(() => ParticipantPresence.fromPrimitives(null as any)).toThrow(
        PersistenceMappingError,
      );
    });

    it('throws PersistenceMappingError for invalid participantId', () => {
      const invalidDto = { ...validPresenceDto, participantId: 'invalid-uuid' };
      expect(() => ParticipantPresence.fromPrimitives(invalidDto)).toThrow(
        PersistenceMappingError,
      );
    });

    it('throws PersistenceMappingError for invalid timestamp', () => {
      const invalidDto = { ...validPresenceDto, joinedAt: -1 };
      expect(() => ParticipantPresence.fromPrimitives(invalidDto)).toThrow(
        PersistenceMappingError,
      );
    });

    it('throws PersistenceMappingError for invalid roomId', () => {
      const invalidDto = { ...validPresenceDto, currentRoomId: 'invalid-uuid' };
      expect(() => ParticipantPresence.fromPrimitives(invalidDto)).toThrow(
        PersistenceMappingError,
      );
    });
  });

  describe('create', () => {
    it('creates new presence with only participantId', () => {
      const participantId = ParticipantId.fromPrimitives(validParticipantId);
      const presence = ParticipantPresence.create(participantId, ParticipantRole.MEMBER);

      expect(presence.participantId).toBe(validParticipantId);
      expect(presence.hasJoined).toBe(false);
      expect(presence.hasLeft).toBe(false);
      expect(presence.isInRoom).toBe(false);
      expect(presence.tags).toEqual({ role: ParticipantRole.MEMBER });
    });
  });

  describe('toPrimitives', () => {
    it('roundtrip preserves data', () => {
      const original = validPresenceDto;
      const presence = ParticipantPresence.fromPrimitives(original);
      const result = presence.toPrimitives();

      expect(result.participantId).toBe(original.participantId);
      expect(result.joinedAt).toBe(original.joinedAt);
      expect(result.currentRoomId).toBe(original.currentRoomId);
      expect(result.tags).toEqual(original.tags);
    });

    it('omits undefined fields', () => {
      const minimalDto: ParticipantPresencePrimitives = {
        participantId: validParticipantId,
        tags: { role: ParticipantRole.MEMBER },
      };

      const presence = ParticipantPresence.fromPrimitives(minimalDto);
      const result = presence.toPrimitives();

      expect(result.joinedAt).toBeUndefined();
      expect(result.leftAt).toBeUndefined();
      expect(result.currentRoomId).toBeUndefined();
      expect(result.tags).toBeDefined();
    });
  });

  describe('onJoin', () => {
    it('sets joinedAt on first join', () => {
      const participantId = ParticipantId.fromPrimitives(validParticipantId);
      const presence = ParticipantPresence.create(participantId, ParticipantRole.MEMBER);
      const at = Instant.fromPrimitives(baseTimestamp);

      const joinedPresence = presence.onJoin(at);

      expect(joinedPresence.hasJoined).toBe(true);
      expect(joinedPresence.hasLeft).toBe(false);
      expect(joinedPresence.joinTime).toBe(baseTimestamp);
      expect(joinedPresence.leaveTime).toBeUndefined();
    });

    it('preserves original joinedAt on subsequent joins', () => {
      const presence = ParticipantPresence.fromPrimitives(validPresenceDto);
      const laterJoin = Instant.fromPrimitives(laterTimestamp);

      const rejoinedPresence = presence.onJoin(laterJoin);

      expect(rejoinedPresence.joinTime).toBe(baseTimestamp); // Original time preserved
    });

    it('clears leftAt when rejoining', () => {
      const presence = ParticipantPresence.fromPrimitives({
        ...validPresenceDto,
        leftAt: laterTimestamp,
      });

      const rejoinedPresence = presence.onJoin(
        Instant.fromPrimitives(evenLaterTimestamp),
      );

      expect(rejoinedPresence.leaveTime).toBeUndefined();
    });

    it('throws PresenceTimeError when join time is before leave time', () => {
      const presence = ParticipantPresence.fromPrimitives({
        ...validPresenceDto,
        leftAt: laterTimestamp,
      });

      const invalidJoinTime = Instant.fromPrimitives(baseTimestamp); // Before leave time

      expect(() => presence.onJoin(invalidJoinTime)).toThrow(PresenceTimeError);
      expect(() => presence.onJoin(invalidJoinTime)).toThrow(
        'Join time cannot be before last leave time',
      );
    });
  });

  describe('onLeave', () => {
    it('sets leftAt and clears currentRoomId', () => {
      const presence = ParticipantPresence.fromPrimitives(validPresenceDto);
      const at = Instant.fromPrimitives(laterTimestamp);

      const leftPresence = presence.onLeave(at);

      expect(leftPresence.hasJoined).toBe(true);
      expect(leftPresence.hasLeft).toBe(true);
      expect(leftPresence.leaveTime).toBe(laterTimestamp);
      expect(leftPresence.isInRoom).toBe(false);
      expect(leftPresence.roomId).toBeUndefined();
    });

    it('throws PresenceTimeError when leave time is before join time', () => {
      const presence = ParticipantPresence.fromPrimitives(validPresenceDto);
      const invalidLeaveTime = Instant.fromPrimitives(baseTimestamp - 1000); // Before join time

      expect(() => presence.onLeave(invalidLeaveTime)).toThrow(
        PresenceTimeError,
      );
      expect(() => presence.onLeave(invalidLeaveTime)).toThrow(
        'Leave time cannot be before join time',
      );
    });
  });

  describe('enterRoom', () => {
    it('sets currentRoomId', () => {
      const presence = ParticipantPresence.fromPrimitives(validPresenceDto);
      const newRoomId = RoomId.fromPrimitives(
        '6ba7b810-9dad-41d1-80b4-00c04fd430c8',
      );
      const at = Instant.fromPrimitives(laterTimestamp);

      const enteredPresence = presence.enterRoom(newRoomId, at);

      expect(enteredPresence.isInRoom).toBe(true);
      expect(enteredPresence.roomId).toBe(
        '6ba7b810-9dad-41d1-80b4-00c04fd430c8',
      );
    });

    it('throws PresenceTimeError when enter time is before join time', () => {
      const presence = ParticipantPresence.fromPrimitives(validPresenceDto);
      const roomId = RoomId.fromPrimitives(validRoomId);
      const invalidEnterTime = Instant.fromPrimitives(baseTimestamp - 1000); // Before join time

      expect(() => presence.enterRoom(roomId, invalidEnterTime)).toThrow(
        PresenceTimeError,
      );
      expect(() => presence.enterRoom(roomId, invalidEnterTime)).toThrow(
        'Room enter time cannot be before join time',
      );
    });

  });

  describe('exitRoom', () => {
    it('clears currentRoomId', () => {
      const presence = ParticipantPresence.fromPrimitives(validPresenceDto);
      const at = Instant.fromPrimitives(laterTimestamp);

      const exitedPresence = presence.exitRoom(at);

      expect(exitedPresence.isInRoom).toBe(false);
      expect(exitedPresence.roomId).toBeUndefined();
    });

    it('throws PresenceTimeError when exit time is before join time', () => {
      const presence = ParticipantPresence.fromPrimitives(validPresenceDto);
      const invalidExitTime = Instant.fromPrimitives(baseTimestamp - 1000); // Before join time

      expect(() => presence.exitRoom(invalidExitTime)).toThrow(
        PresenceTimeError,
      );
      expect(() => presence.exitRoom(invalidExitTime)).toThrow(
        'Room exit time cannot be before join time',
      );
    });
  });

  describe('updateTags', () => {
    it('updates tags', () => {
      const presence = ParticipantPresence.fromPrimitives(validPresenceDto);
      const newTags = { role: ParticipantRole.MEMBER };

      const updatedPresence = presence.updateTags(newTags);

      expect(updatedPresence.tags).toEqual({...presence.tags, ...newTags});
    });

    it('returns same instance when tags unchanged', () => {
      const presence = ParticipantPresence.fromPrimitives(validPresenceDto);
      const sameTags = { role: ParticipantRole.MEMBER }; // Same as current

      const updatedPresence = presence.updateTags(sameTags);

      expect(updatedPresence).toBe(presence); // Same instance when no change
    });
  });

  describe('state getters', () => {
    it('hasJoined returns correct state', () => {
      const notJoined = ParticipantPresence.create(
        ParticipantId.fromPrimitives(validParticipantId),
        ParticipantRole.MEMBER,
      );
      expect(notJoined.hasJoined).toBe(false);

      const joined = ParticipantPresence.fromPrimitives(validPresenceDto);
      expect(joined.hasJoined).toBe(true);
    });

    it('hasLeft returns correct state', () => {
      const joined = ParticipantPresence.fromPrimitives(validPresenceDto);
      expect(joined.hasLeft).toBe(false);

      const left = ParticipantPresence.fromPrimitives({
        ...validPresenceDto,
        leftAt: laterTimestamp,
      });
      expect(left.hasLeft).toBe(true);
    });

    it('isInRoom returns correct state', () => {
      const notInRoom = ParticipantPresence.fromPrimitives({
        participantId: validParticipantId,
        joinedAt: baseTimestamp,
        tags: { role: ParticipantRole.MEMBER },
      });
      expect(notInRoom.isInRoom).toBe(false);

      const inRoom = ParticipantPresence.fromPrimitives(validPresenceDto);
      expect(inRoom.isInRoom).toBe(true);
    });
  });

  describe('error context preservation', () => {
    it('preserves context in PresenceTimeError', () => {
      const presence = ParticipantPresence.fromPrimitives(validPresenceDto);
      const invalidLeaveTime = Instant.fromPrimitives(baseTimestamp - 1000);

      try {
        presence.onLeave(invalidLeaveTime);
      } catch (error) {
        expect(error).toBeInstanceOf(PresenceTimeError);
        expect((error as PresenceTimeError).code).toBe(
          ERR_ENTITY_PRESENCE_TIME,
        );
        expect((error as PresenceTimeError).ctx?.leaveTime).toBe(
          baseTimestamp - 1000,
        );
        expect((error as PresenceTimeError).ctx?.joinTime).toBe(baseTimestamp);
        expect((error as PresenceTimeError).ctx?.participantId).toBe(
          validParticipantId,
        );
      }
    });
  });
});
