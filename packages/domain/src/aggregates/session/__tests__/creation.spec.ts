import { Session } from '../session.aggregate';
import { SessionId } from '../../../value-objects/sessions/session-id/session-id.vo';
import { SessionConfig } from '../../../value-objects/sessions/session-config/session-config.vo';
import { SessionConfigPrimitives } from '../../../value-objects/sessions/contracts/session-config.type';
import { SessionState } from '../../../value-objects/sessions/contracts/session.state.enum';
import { SessionMode } from '../../../value-objects/sessions/contracts/session-mode.enum';
import { RoomId } from '../../../value-objects/rooms/room-id/room-id.vo';
import { Uuid } from '../../../primitives/uuid/uuid.primitive';
import { LateJoinAllocationMode } from '../../../policies/late-join/types/late-join-allocation-mode.enum';
import { AllocationStrategy } from '../../../policies/autopilot/types/allocation-strategy.enum';

describe('Session - Creation', () => {
  const validSessionId = '550e8400-e29b-41d4-a716-************';
  const validAdminId = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
  const validMainRoomId = '6ba7b810-9dad-41d1-80b4-00c04fd430c8';
  const baseTimestamp = 1640995200000; // 2022-01-01 00:00:00 UTC

  const validSessionConfig: SessionConfigPrimitives = {
    // Scheduling
    scheduledStartAt: baseTimestamp + 300000, // 5 minutes later
    estimatedDurationMs: 3600000, // 1 hour
    
    // Rooms configuration
    defaultRoomConfig: {
      minSeats: 2,
      maxSeats: 8,
      avoidSingleton: true,
      disconnectionPolicy: { holdSeatForMs: 30000 },
    },
    maxParticipants: 50,
    
    // Rounds configuration
    defaultRoundDurationMs: 900000, // 15 minutes
    
    // Policies
    autopilotPolicy: {
      allocationStategy: AllocationStrategy.ROUND_ROBIN,
    },
    lobbyAdmissionPolicy: {
      allowEarlyJoinMs: 300000, // 5 minutes
      requireHostPresent: true
    },
    disconnectionPolicy: {
      holdSeatForMs: 120000, // 2 minutes
    },
    lateJoinPolicy: {
      allocationMode: LateJoinAllocationMode.BEST_FIT
    },
    
    // Session mode
    mode: SessionMode.HOSTED,
  };

  describe('Happy path', () => {
    it('creates session with correct initial state', () => {
      const sessionId = SessionId.fromPrimitives(validSessionId);
      const config = SessionConfig.fromPrimitives(validSessionConfig);
      const adminId = Uuid.fromPrimitives(validAdminId);
      const mainRoomId = RoomId.fromPrimitives(validMainRoomId);

      const session = Session.create(sessionId, config, adminId, mainRoomId);

      // Verify basic properties through public API
      const primitives = session.toPrimitives();
      expect(primitives.sessionId).toBe(validSessionId);
      expect(primitives.createdByUserId).toBe(validAdminId);
      expect(primitives.mainRoomId).toBe(validMainRoomId);
      expect(session.state).toBe(SessionState.SCHEDULED);
    });

    it('creates session without host initially', () => {
      const sessionId = SessionId.fromPrimitives(validSessionId);
      const config = SessionConfig.fromPrimitives(validSessionConfig);
      const adminId = Uuid.fromPrimitives(validAdminId);
      const mainRoomId = RoomId.fromPrimitives(validMainRoomId);

      const session = Session.create(sessionId, config, adminId, mainRoomId);

      expect(session.hasHost).toBe(false);
    });

    it('creates session with empty participants and rounds initially', () => {
      const sessionId = SessionId.fromPrimitives(validSessionId);
      const config = SessionConfig.fromPrimitives(validSessionConfig);
      const adminId = Uuid.fromPrimitives(validAdminId);
      const mainRoomId = RoomId.fromPrimitives(validMainRoomId);

      const session = Session.create(sessionId, config, adminId, mainRoomId);

      expect(session.participants).toHaveLength(0);
      expect(session.rounds).toHaveLength(0);
    });

    it('creates main room with correct configuration', () => {
      const sessionId = SessionId.fromPrimitives(validSessionId);
      const config = SessionConfig.fromPrimitives(validSessionConfig);
      const adminId = Uuid.fromPrimitives(validAdminId);
      const mainRoomId = RoomId.fromPrimitives(validMainRoomId);

      const session = Session.create(sessionId, config, adminId, mainRoomId);

      expect(session.rooms).toHaveLength(1);
      
      const mainRoom = session.findRoomById(mainRoomId);
      expect(mainRoom).toBeDefined();
      expect(mainRoom!.roomId.toPrimitives()).toBe(validMainRoomId);
      
      // Main room should have maxParticipants as maxSeats (not defaultRoomConfig.maxSeats)
      expect(mainRoom!.allSeats).toHaveLength(config.maxParticipants);
    });

    it('preserves session config correctly', () => {
      const sessionId = SessionId.fromPrimitives(validSessionId);
      const config = SessionConfig.fromPrimitives(validSessionConfig);
      const adminId = Uuid.fromPrimitives(validAdminId);
      const mainRoomId = RoomId.fromPrimitives(validMainRoomId);

      const session = Session.create(sessionId, config, adminId, mainRoomId);

      const sessionPrimitives = session.toPrimitives();
      expect(sessionPrimitives.config).toEqual(validSessionConfig);
    });
  });

  describe('State checks', () => {
    it('has correct initial state flags', () => {
      const sessionId = SessionId.fromPrimitives(validSessionId);
      const config = SessionConfig.fromPrimitives(validSessionConfig);
      const adminId = Uuid.fromPrimitives(validAdminId);
      const mainRoomId = RoomId.fromPrimitives(validMainRoomId);

      const session = Session.create(sessionId, config, adminId, mainRoomId);

      expect(session.isScheduled).toBe(true);
      expect(session.isRunning).toBe(false);
      expect(session.isPaused).toBe(false);
      expect(session.isCompleted).toBe(false);
      expect(session.isCanceled).toBe(false);
    });
  });

  describe('Room queries', () => {
    it('provides correct room query results for new session', () => {
      const sessionId = SessionId.fromPrimitives(validSessionId);
      const config = SessionConfig.fromPrimitives(validSessionConfig);
      const adminId = Uuid.fromPrimitives(validAdminId);
      const mainRoomId = RoomId.fromPrimitives(validMainRoomId);

      const session = Session.create(sessionId, config, adminId, mainRoomId);

      expect(session.getRoomsWithSpace()).toHaveLength(1);
      expect(session.getFillingRooms()).toHaveLength(1); // Main room starts empty (FILLING)
      expect(session.getReadyRooms()).toHaveLength(0); // Main room needs participants
    });
  });
});
