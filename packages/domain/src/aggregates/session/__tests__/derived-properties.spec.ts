import { Session } from '../session.aggregate';
import { SessionId } from '../../../value-objects/sessions/session-id/session-id.vo';
import { SessionConfig } from '../../../value-objects/sessions/session-config/session-config.vo';
import { SessionConfigPrimitives } from '../../../value-objects/sessions/contracts/session-config.type';
import { SessionState } from '../../../value-objects/sessions/contracts/session.state.enum';
import { SessionMode } from '../../../value-objects/sessions/contracts/session-mode.enum';
import { RoomId } from '../../../value-objects/rooms/room-id/room-id.vo';
import { ParticipantId } from '../../../value-objects/participants/participant-id/participant-id.vo';
import { ParticipantRole } from '../../../value-objects/participants/contracts/participant-role.enum';
import { Uuid } from '../../../primitives/uuid/uuid.primitive';
import { Instant } from '../../../primitives/instant/instant.primitive';
import { PositiveInt } from '../../../primitives/positive-int/positive-int.primitive';
import { Round } from '../../../value-objects/sessions/round/round.entity';
import { RoundKind } from '../../../value-objects/sessions/round/contracts/round-kind.enum';
import { LateJoinAllocationMode } from '../../../policies/late-join/types/late-join-allocation-mode.enum';
import { AllocationStrategy } from '../../../policies/autopilot/types/allocation-strategy.enum';

describe('Session: Derived Properties', () => {
  const validSessionId = '550e8400-e29b-41d4-a716-************';
  const validAdminId = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
  const validMainRoomId = '6ba7b810-9dad-41d1-80b4-00c04fd430c8';
  const validParticipantId1 = '123e4567-e89b-42d3-a456-************';
  const validParticipantId2 = '987fcdeb-51a2-43d1-9f12-************';
  const baseTimestamp = 1640995200000; // 2022-01-01 00:00:00 UTC

  const validSessionConfig: SessionConfigPrimitives = {
    scheduledStartAt: baseTimestamp + 300000,
    estimatedDurationMs: 3600000,
    defaultRoomConfig: {
      minSeats: 2,
      maxSeats: 8,
      avoidSingleton: true,
      disconnectionPolicy: { holdSeatForMs: 30000 },
    },
    maxParticipants: 50,
    defaultRoundDurationMs: 900000,
    autopilotPolicy: {
      allocationStategy: AllocationStrategy.ROUND_ROBIN,
    },
    lobbyAdmissionPolicy: {
      allowEarlyJoinMs: 300000,
      requireHostPresent: true
    },
    disconnectionPolicy: {
      holdSeatForMs: 120000,
    },
    lateJoinPolicy: {
      allocationMode: LateJoinAllocationMode.BEST_FIT
    },
    mode: SessionMode.HOSTED,
  };

  function createTestSession(): Session {
    const sessionId = SessionId.fromPrimitives(validSessionId);
    const config = SessionConfig.fromPrimitives(validSessionConfig);
    const adminId = Uuid.fromPrimitives(validAdminId);
    const mainRoomId = RoomId.fromPrimitives(validMainRoomId);

    return Session.create(sessionId, config, adminId, mainRoomId);
  }

  describe('State getters', () => {
    it('returns correct state value', () => {
      const session = createTestSession();

      expect(session.state).toBe(SessionState.SCHEDULED);
    });

    describe('isScheduled', () => {
      it('returns true when session is SCHEDULED', () => {
        const session = createTestSession();
        expect(session.isScheduled).toBe(true);
      });

      it('returns false when session is not SCHEDULED', () => {
        const session = createTestSession();
        const runningSession = session.startSession();
        expect(runningSession.isScheduled).toBe(false);
      });
    });

    describe('isRunning', () => {
      it('returns true when session is RUNNING', () => {
        const session = createTestSession();
        const runningSession = session.startSession();
        expect(runningSession.isRunning).toBe(true);
      });

      it('returns false when session is not RUNNING', () => {
        const session = createTestSession();
        expect(session.isRunning).toBe(false);
      });
    });

    describe('isPaused', () => {
      it('returns true when session is PAUSED', () => {
        const session = createTestSession();
        const runningSession = session.startSession();
        const pausedSession = runningSession.pauseSession();
        expect(pausedSession.isPaused).toBe(true);
      });

      it('returns false when session is not PAUSED', () => {
        const session = createTestSession();
        expect(session.isPaused).toBe(false);
      });
    });

    describe('isCompleted', () => {
      it('returns true when session is COMPLETED', () => {
        const session = createTestSession();
        const runningSession = session.startSession();
        const completedSession = runningSession.completeSession();
        expect(completedSession.isCompleted).toBe(true);
      });

      it('returns false when session is not COMPLETED', () => {
        const session = createTestSession();
        expect(session.isCompleted).toBe(false);
      });
    });

    describe('isCanceled', () => {
      it('returns true when session is CANCELED', () => {
        const session = createTestSession();
        const canceledSession = session.cancelSession();
        expect(canceledSession.isCanceled).toBe(true);
      });

      it('returns false when session is not CANCELED', () => {
        const session = createTestSession();
        expect(session.isCanceled).toBe(false);
      });
    });
  });

  describe('currentRound getter', () => {
    it('returns undefined when no round is active', () => {
      const session = createTestSession();
      expect(session.currentRound).toBeUndefined();
    });

    it('returns current round when one is active', () => {
      let session = createTestSession();
      const round = Round.fromPrimitives({
        roundNo: 1,
        kind: RoundKind.MAIN_TOPIC,
        durationMs: 900000,
        questions: ['What is your favorite color?'],
      });

      session = session.addRound(round);
      session = session.startSession();
      session = session.startRound(PositiveInt.fromPrimitives(1));

      const currentRound = session.currentRound;
      expect(currentRound).toBeDefined();
      expect(currentRound!.roundNo.toPrimitives()).toBe(1);
      expect(currentRound!.questions).toEqual(['What is your favorite color?']);
    });

    it('returns undefined after round is ended', () => {
      let session = createTestSession();
      const round = Round.fromPrimitives({
        roundNo: 1,
        kind: RoundKind.MAIN_TOPIC,
        durationMs: 900000,
        questions: ['Test question'],
      });

      session = session.addRound(round);
      session = session.startSession();
      session = session.startRound(PositiveInt.fromPrimitives(1));
      session = session.endCurrentRound();

      expect(session.currentRound).toBeUndefined();
    });
  });

  describe('rounds getter', () => {
    it('returns empty array when no rounds added', () => {
      const session = createTestSession();
      expect(session.rounds).toEqual([]);
      expect(session.rounds).toHaveLength(0);
    });

    it('returns copy of rounds array', () => {
      let session = createTestSession();
      const round1 = Round.fromPrimitives({
        roundNo: 1,
        kind: RoundKind.MAIN_TOPIC,
        durationMs: 900000,
        questions: ['Question 1'],
      });
      const round2 = Round.fromPrimitives({
        roundNo: 2,
        kind: RoundKind.MAIN_TOPIC,
        durationMs: 900000,
        questions: ['Question 2'],
      });

      session = session.addRound(round1);
      session = session.addRound(round2);

      const rounds = session.rounds;
      expect(rounds).toHaveLength(2);
      expect(rounds[0].roundNo.toPrimitives()).toBe(1);
      expect(rounds[1].roundNo.toPrimitives()).toBe(2);

      // Verify it's a copy (mutations don't affect original)
      rounds.pop();
      expect(session.rounds).toHaveLength(2);
    });
  });

  describe('participants getter', () => {
    it('returns empty array when no participants added', () => {
      const session = createTestSession();
      expect(session.participants).toEqual([]);
      expect(session.participants).toHaveLength(0);
    });

    it('returns copy of participants array', () => {
      let session = createTestSession();
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      const participant1 = ParticipantId.fromPrimitives(validParticipantId1);
      const participant2 = ParticipantId.fromPrimitives(validParticipantId2);

      session = session.addParticipant(participant1, at, ParticipantRole.MEMBER);
      session = session.addParticipant(participant2, at, ParticipantRole.HOST);

      const participants = session.participants;
      expect(participants).toHaveLength(2);
      expect(participants[0].participantId).toBe(validParticipantId1);
      expect(participants[1].participantId).toBe(validParticipantId2);

      // Verify it's a copy (mutations don't affect original)
      participants.pop();
      expect(session.participants).toHaveLength(2);
    });
  });

  describe('rooms getter', () => {
    it('returns array with main room initially', () => {
      const session = createTestSession();
      const rooms = session.rooms;

      expect(rooms).toHaveLength(1);
      expect(rooms[0].roomId.toPrimitives()).toBe(validMainRoomId);
    });

    it('returns copy of rooms array', () => {
      let session = createTestSession();
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      session = session.startSession();
      const breakoutRoomId = RoomId.fromPrimitives('12345678-1234-4567-8901-234567890123');
      session = session.createBreakoutRoom(breakoutRoomId, at);

      const rooms = session.rooms;
      expect(rooms).toHaveLength(2);

      // Verify it's a copy (mutations don't affect original)
      rooms.pop();
      expect(session.rooms).toHaveLength(2);
    });
  });

  describe('hasHost getter', () => {
    it('returns false when no host is set', () => {
      const session = createTestSession();
      expect(session.hasHost).toBe(false);
    });

    it('returns true when host is set', () => {
      let session = createTestSession();
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      const hostParticipant = ParticipantId.fromPrimitives(validParticipantId1);
      session = session.addParticipant(hostParticipant, at, ParticipantRole.HOST);
      session = session.setHost(hostParticipant);

      expect(session.hasHost).toBe(true);
    });
  });

  describe('Utility methods', () => {
    describe('findParticipantById', () => {
      it('returns participant when found', () => {
        let session = createTestSession();
        const at = Instant.fromPrimitives(baseTimestamp + 1000);

        const participantId = ParticipantId.fromPrimitives(validParticipantId1);
        session = session.addParticipant(participantId, at, ParticipantRole.MEMBER);

        const foundParticipant = session.findParticipantById(participantId);
        expect(foundParticipant).toBeDefined();
        expect(foundParticipant!.participantId).toBe(validParticipantId1);
      });

      it('returns undefined when participant not found', () => {
        const session = createTestSession();
        const nonExistentParticipant = ParticipantId.fromPrimitives(validParticipantId1);

        const foundParticipant = session.findParticipantById(nonExistentParticipant);
        expect(foundParticipant).toBeUndefined();
      });
    });

    describe('getParticipantRoom', () => {
      it('returns room where participant is seated', () => {
        let session = createTestSession();
        const at = Instant.fromPrimitives(baseTimestamp + 1000);

        const participantId = ParticipantId.fromPrimitives(validParticipantId1);
        session = session.startSession();
        session = session.onJoin(participantId, at);

        const participantRoom = session.getParticipantRoom(participantId);
        expect(participantRoom).toBeDefined();
        expect(participantRoom!.roomId.toPrimitives()).toBe(validMainRoomId);
      });

      it('returns undefined when participant is not seated', () => {
        let session = createTestSession();
        const at = Instant.fromPrimitives(baseTimestamp + 1000);

        const participantId = ParticipantId.fromPrimitives(validParticipantId1);
        session = session.addParticipant(participantId, at, ParticipantRole.MEMBER);

        const participantRoom = session.getParticipantRoom(participantId);
        expect(participantRoom).toBeUndefined();
      });
    });
  });
});
