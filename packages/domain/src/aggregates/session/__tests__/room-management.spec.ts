import { Session } from '../session.aggregate';
import { SessionId } from '../../../value-objects/sessions/session-id/session-id.vo';
import { SessionConfig } from '../../../value-objects/sessions/session-config/session-config.vo';
import { SessionConfigPrimitives } from '../../../value-objects/sessions/contracts/session-config.type';
import { SessionMode } from '../../../value-objects/sessions/contracts/session-mode.enum';
import { RoomId } from '../../../value-objects/rooms/room-id/room-id.vo';
import { ParticipantId } from '../../../value-objects/participants/participant-id/participant-id.vo';
import { Uuid } from '../../../primitives/uuid/uuid.primitive';
import { Instant } from '../../../primitives/instant/instant.primitive';
import { PositiveInt } from '../../../primitives/positive-int/positive-int.primitive';
import { Round } from '../../../value-objects/sessions/round/round.entity';
import { RoundKind } from '../../../value-objects/sessions/round/contracts/round-kind.enum';
import { SessionStateError } from '../session.errors';
import { LateJoinAllocationMode } from '../../../policies/late-join/types/late-join-allocation-mode.enum';
import { AllocationStrategy } from '../../../policies/autopilot/types/allocation-strategy.enum';

describe('Session: Room Management', () => {
  const validSessionId = '550e8400-e29b-41d4-a716-************';
  const validAdminId = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
  const validMainRoomId = '6ba7b810-9dad-41d1-80b4-00c04fd430c8';
  const validParticipantId1 = '123e4567-e89b-42d3-a456-************';
  const validParticipantId2 = '987fcdeb-51a2-43d1-9f12-************';
  const validParticipantId3 = 'abcdef12-3456-4890-abcd-ef1234567890';
  const validParticipantId4 = 'fedcba98-7654-4210-aedc-ba9876543210';
  const baseTimestamp = 1640995200000; // 2022-01-01 00:00:00 UTC

  const validSessionConfig: SessionConfigPrimitives = {
    scheduledStartAt: baseTimestamp + 300000,
    estimatedDurationMs: 3600000,
    defaultRoomConfig: {
      minSeats: 2,
      maxSeats: 4,
      avoidSingleton: true,
      disconnectionPolicy: { holdSeatForMs: 30000 },
    },
    maxParticipants: 50,
    defaultRoundDurationMs: 900000,
    autopilotPolicy: {
      allocationStategy: AllocationStrategy.ROUND_ROBIN,
    },
    lobbyAdmissionPolicy: {
      allowEarlyJoinMs: 300000,
      requireHostPresent: true
    },
    disconnectionPolicy: {
      holdSeatForMs: 120000,
    },
    lateJoinPolicy: {
      allocationMode: LateJoinAllocationMode.BEST_FIT
    },
    mode: SessionMode.HOSTED,
  };

  function createTestSession(): Session {
    const sessionId = SessionId.fromPrimitives(validSessionId);
    const config = SessionConfig.fromPrimitives(validSessionConfig);
    const adminId = Uuid.fromPrimitives(validAdminId);
    const mainRoomId = RoomId.fromPrimitives(validMainRoomId);

    return Session.create(sessionId, config, adminId, mainRoomId);
  }

  function createRunningSession(): Session {
    return createTestSession().startSession();
  }

  describe('createBreakoutRoom', () => {
    it('creates new breakout room', () => {
      const session = createRunningSession();
      const breakoutRoomId = RoomId.fromPrimitives('12345678-1234-4567-8901-************');
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      const updatedSession = session.createBreakoutRoom(breakoutRoomId, at);

      expect(updatedSession.rooms).toHaveLength(2); // Main room + breakout room
      
      const breakoutRoom = updatedSession.findRoomById(breakoutRoomId);
      expect(breakoutRoom).toBeDefined();
      expect(breakoutRoom!.roomId.toPrimitives()).toBe(breakoutRoomId.toPrimitives());
      expect(breakoutRoom!.allSeats).toHaveLength(4); // defaultRoomConfig.maxSeats
    });

    it('is idempotent when room already exists', () => {
      const session = createRunningSession();
      const breakoutRoomId = RoomId.fromPrimitives('12345678-1234-4567-8901-************');
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      const sessionWithRoom = session.createBreakoutRoom(breakoutRoomId, at);
      const sessionAgain = sessionWithRoom.createBreakoutRoom(breakoutRoomId, at);

      expect(sessionAgain.rooms).toHaveLength(2);
      expect(sessionAgain).toBe(sessionWithRoom); // Should return same instance
    });

    it('throws SessionStateError when session is not RUNNING', () => {
      const session = createTestSession();
      const breakoutRoomId = RoomId.fromPrimitives('12345678-1234-4567-8901-************');
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      expect(() => session.createBreakoutRoom(breakoutRoomId, at)).toThrow(SessionStateError);
    });
  });

  describe('findRoomById', () => {
    it('finds main room by id', () => {
      const session = createTestSession();
      const mainRoomId = RoomId.fromPrimitives(validMainRoomId);

      const mainRoom = session.findRoomById(mainRoomId);

      expect(mainRoom).toBeDefined();
      expect(mainRoom!.roomId.toPrimitives()).toBe(validMainRoomId);
    });

    it('finds breakout room by id', () => {
      const session = createRunningSession();
      const breakoutRoomId = RoomId.fromPrimitives('12345678-1234-4567-8901-************');
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      const sessionWithRoom = session.createBreakoutRoom(breakoutRoomId, at);
      const breakoutRoom = sessionWithRoom.findRoomById(breakoutRoomId);

      expect(breakoutRoom).toBeDefined();
      expect(breakoutRoom!.roomId.toPrimitives()).toBe(breakoutRoomId.toPrimitives());
    });

    it('returns undefined for non-existent room', () => {
      const session = createTestSession();
      const nonExistentRoomId = RoomId.fromPrimitives('99999999-9999-4999-8999-999999999999');

      const room = session.findRoomById(nonExistentRoomId);

      expect(room).toBeUndefined();
    });
  });

  describe('returnAllParticipantsToMain', () => {
    it('moves all participants from breakout rooms to main room', () => {
      let session = createRunningSession();
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      // Add participants
      const participant1 = ParticipantId.fromPrimitives(validParticipantId1);
      const participant2 = ParticipantId.fromPrimitives(validParticipantId2);
      session = session.onJoin(participant1, at);
      session = session.onJoin(participant2, at);

      // Create breakout room and move participants
      const breakoutRoomId = RoomId.fromPrimitives('12345678-1234-4567-8901-************');
      session = session.createBreakoutRoom(breakoutRoomId, at);
      session = session.assignParticipantToRoom(participant1, breakoutRoomId, at);
      session = session.assignParticipantToRoom(participant2, breakoutRoomId, at);

      // Verify participants are in breakout room
      const breakoutRoom = session.findRoomById(breakoutRoomId);
      expect(breakoutRoom!.size).toBe(2);

      const mainRoomId = RoomId.fromPrimitives(validMainRoomId);
      const mainRoom = session.findRoomById(mainRoomId);
      expect(mainRoom!.size).toBe(0);

      // Return all to main
      const returnedSession = session.returnAllParticipantsToMain(at);

      const mainRoomAfter = returnedSession.findRoomById(mainRoomId);
      expect(mainRoomAfter!.size).toBe(2);

      const breakoutRoomAfter = returnedSession.findRoomById(breakoutRoomId);
      expect(breakoutRoomAfter!.size).toBe(0);
    });

    it('throws SessionStateError when session is not RUNNING', () => {
      const session = createTestSession();
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      expect(() => session.returnAllParticipantsToMain(at)).toThrow(SessionStateError);
    });
  });

  describe('rotateBreakouts', () => {
    it('rotates participants across breakout rooms using diagonal spread', () => {
      let session = createRunningSession();
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      // Add participants
      const participant1 = ParticipantId.fromPrimitives(validParticipantId1);
      const participant2 = ParticipantId.fromPrimitives(validParticipantId2);
      const participant3 = ParticipantId.fromPrimitives(validParticipantId3);
      const participant4 = ParticipantId.fromPrimitives(validParticipantId4);

      session = session.onJoin(participant1, at);
      session = session.onJoin(participant2, at);
      session = session.onJoin(participant3, at);
      session = session.onJoin(participant4, at);

      // Create breakout rooms
      const breakoutRoom1Id = RoomId.fromPrimitives('11111111-1111-4111-8111-111111111111');
      const breakoutRoom2Id = RoomId.fromPrimitives('22222222-2222-4222-8222-222222222222');
      session = session.createBreakoutRoom(breakoutRoom1Id, at);
      session = session.createBreakoutRoom(breakoutRoom2Id, at);

      // Assign participants to breakout rooms
      session = session.assignParticipantToRoom(participant1, breakoutRoom1Id, at);
      session = session.assignParticipantToRoom(participant2, breakoutRoom1Id, at);
      session = session.assignParticipantToRoom(participant3, breakoutRoom2Id, at);
      session = session.assignParticipantToRoom(participant4, breakoutRoom2Id, at);

      // Start a round to enable rotation
      const round = Round.fromPrimitives({
        roundNo: 1,
        kind: RoundKind.MAIN_TOPIC,
        durationMs: 900000,
        questions: ['Test question'],
      });
      session = session.addRound(round);
      session = session.startRound(PositiveInt.fromPrimitives(1));

      // Verify initial state
      const room1Before = session.findRoomById(breakoutRoom1Id);
      const room2Before = session.findRoomById(breakoutRoom2Id);
      expect(room1Before!.size).toBe(2);
      expect(room2Before!.size).toBe(2);

      const room1ParticipantsBefore = room1Before!.occupiedSeats.map(seat => seat.currentParticipantId);
      const room2ParticipantsBefore = room2Before!.occupiedSeats.map(seat => seat.currentParticipantId);

      expect(room1ParticipantsBefore).toEqual([validParticipantId1, validParticipantId2]);
      expect(room2ParticipantsBefore).toEqual([validParticipantId3, validParticipantId4]);

      // Rotate
      const rotatedSession = session.rotateBreakouts(at);

      // Verify participants have been redistributed
      const room1After = rotatedSession.findRoomById(breakoutRoom1Id);
      const room2After = rotatedSession.findRoomById(breakoutRoom2Id);
      expect(room1After!.size).toBe(2);
      expect(room2After!.size).toBe(2);

      // Verify that participants have moved (diagonal spread pattern)
      // With 2 rooms and 2 participants per room, each participant should move to the other room
      const room1ParticipantsAfter = room1After!.occupiedSeats.map(seat => seat.currentParticipantId);
      const room2ParticipantsAfter = room2After!.occupiedSeats.map(seat => seat.currentParticipantId);

      // The participants should have been redistributed
      expect(new Set(room1ParticipantsAfter)).toEqual(new Set([validParticipantId1, validParticipantId4]));
      expect(new Set(room2ParticipantsAfter)).toEqual(new Set([validParticipantId2, validParticipantId3]));

    });

    it('is no-op when no round is active', () => {
      const session = createRunningSession();
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      const result = session.rotateBreakouts(at);

      expect(result).toBe(session); // Should return same instance
    });

    it('is no-op when there are 0 or 1 breakout rooms', () => {
      let session = createRunningSession();
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      // Start a round
      const round = Round.fromPrimitives({
        roundNo: 1,
        kind: RoundKind.MAIN_TOPIC,
        durationMs: 900000,
        questions: ['Test question'],
      });
      session = session.addRound(round);
      session = session.startRound(PositiveInt.fromPrimitives(1));

      // No breakout rooms - should be no-op
      const resultNoRooms = session.rotateBreakouts(at);
      expect(resultNoRooms).toBe(session);

      // One breakout room - should be no-op
      const breakoutRoomId = RoomId.fromPrimitives('12345678-1234-4567-8901-************');
      const sessionWithOneRoom = session.createBreakoutRoom(breakoutRoomId, at);
      const resultOneRoom = sessionWithOneRoom.rotateBreakouts(at);
      expect(resultOneRoom).toBe(sessionWithOneRoom);
    });

    it('throws SessionStateError when session is not RUNNING', () => {
      const session = createTestSession();
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      expect(() => session.rotateBreakouts(at)).toThrow(SessionStateError);
    });
  });

  describe('Room query methods', () => {
    it('getRoomsWithSpace returns rooms that have available seats', () => {
      let session = createRunningSession();
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      // Initially only main room exists and has space
      expect(session.getRoomsWithSpace()).toHaveLength(1);

      // Create breakout room
      const breakoutRoomId = RoomId.fromPrimitives('12345678-1234-4567-8901-************');
      session = session.createBreakoutRoom(breakoutRoomId, at);

      // Now both rooms have space
      expect(session.getRoomsWithSpace()).toHaveLength(2);
    });

    it('getFillingRooms returns rooms that are filling but not ready', () => {
      let session = createRunningSession();
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      // Add one participant to main room
      const participant1 = ParticipantId.fromPrimitives(validParticipantId1);
      session = session.onJoin(participant1, at);

      const fillingRooms = session.getFillingRooms();
      expect(fillingRooms).toHaveLength(1);
      expect(fillingRooms[0].roomId.toPrimitives()).toBe(validMainRoomId);
    });

    it('getReadyRooms returns rooms that meet minimum seat requirements', () => {
      let session = createRunningSession();
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      // Add two participants to main room (meets minSeats = 2)
      const participant1 = ParticipantId.fromPrimitives(validParticipantId1);
      const participant2 = ParticipantId.fromPrimitives(validParticipantId2);
      session = session.onJoin(participant1, at);
      session = session.onJoin(participant2, at);

      const readyRooms = session.getReadyRooms();
      expect(readyRooms).toHaveLength(1);
      expect(readyRooms[0].roomId.toPrimitives()).toBe(validMainRoomId);
    });
  });
});
