import { Session } from '../session.aggregate';
import { SessionId } from '../../../value-objects/sessions/session-id/session-id.vo';
import { SessionConfig } from '../../../value-objects/sessions/session-config/session-config.vo';
import { ParticipantId } from '../../../value-objects/participants/participant-id/participant-id.vo';
import { RoomId } from '../../../value-objects/rooms/room-id/room-id.vo';
import { Instant } from '../../../primitives/instant/instant.primitive';
import { PositiveInt } from '../../../primitives/positive-int/positive-int.primitive';
import { Round } from '../../../value-objects/sessions/round/round.entity';
import { RoundKind } from '../../../value-objects/sessions/round/contracts/round-kind.enum';
import { SessionConfigPrimitives } from '../../../value-objects/sessions/contracts/session-config.type';
import { LateJoinAllocationMode } from '../../../policies/late-join/types/late-join-allocation-mode.enum';
import { AllocationStrategy } from '../../../policies/autopilot/types/allocation-strategy.enum';
import { SessionMode } from '../../../value-objects/sessions/contracts/session-mode.enum';
import { Uuid } from '../../../primitives/uuid/uuid.primitive';

describe('Session: Live Session Scenarios with 50 Participants', () => {
  const validSessionId = '550e8400-e29b-41d4-a716-************';
  const validAdminId = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
  const validMainRoomId = '6ba7b810-9dad-41d1-80b4-00c04fd430c8';
  const baseTimestamp = 1640995200000; // 2022-01-01 00:00:00 UTC

  const validSessionConfig: SessionConfigPrimitives = {
    scheduledStartAt: baseTimestamp + 300000,
    estimatedDurationMs: 3600000,
    defaultRoomConfig: {
      minSeats: 2,
      maxSeats: 4, // Small breakout rooms
      avoidSingleton: true,
      disconnectionPolicy: { holdSeatForMs: 30000 },
    },
    maxParticipants: 100, // Allow for 50+ participants
    defaultRoundDurationMs: 900000,
    autopilotPolicy: {
      allocationStategy: AllocationStrategy.ROUND_ROBIN,
    },
    lobbyAdmissionPolicy: {
      allowEarlyJoinMs: 300000,
      requireHostPresent: true,
    },
    disconnectionPolicy: {
      holdSeatForMs: 120000,
    },
    lateJoinPolicy: {
      allocationMode: LateJoinAllocationMode.BEST_FIT,
    },
    mode: SessionMode.HOSTED,
  };

  // Helper function to generate 50 unique participant IDs
  const generateParticipantIds = (count: number): string[] => {
    return Array.from({ length: count }, (_, i) => {
      const paddedIndex = i.toString().padStart(3, '0');
      return `12345678-1234-4567-8${paddedIndex.slice(0, 3)}-${paddedIndex}123456789`;
    });
  };

  const createLiveSession = (): Session => {
    const sessionId = SessionId.fromPrimitives(validSessionId);
    const adminId = Uuid.fromPrimitives(validAdminId);
    const mainRoomId = RoomId.fromPrimitives(validMainRoomId);
    const config = SessionConfig.fromPrimitives(validSessionConfig);

    return Session.create(
      sessionId,
      config,
      adminId,
      mainRoomId,
    ).startSession();
  };

  const createRound = (
    roundNo: number,
    kind: RoundKind = RoundKind.MAIN_TOPIC,
  ): Round => {
    return Round.fromPrimitives({
      roundNo: roundNo,
      kind: kind,
      durationMs: validSessionConfig.defaultRoundDurationMs,
      questions: ['Question for round ' + roundNo],
    });
  };

  describe('Massive Participant Joining During Active Round', () => {
    it.each([
      { participantCount: 5, expectedBreakoutRooms: 2 },
      { participantCount: 10, expectedBreakoutRooms: 3 },
      { participantCount: 25, expectedBreakoutRooms: 7 },
      { participantCount: 60, expectedBreakoutRooms: 15 },
      { participantCount: 100, expectedBreakoutRooms: 25 },
    ])(
      'handles $participantCount participants joining during round - creates $expectedBreakoutRooms breakout rooms',
      ({ participantCount, expectedBreakoutRooms }) => {
        let session = createLiveSession();
        const participantIds = generateParticipantIds(participantCount);
        const at = Instant.fromPrimitives(baseTimestamp + 1000);

        // Add and start a round first
        const round1 = createRound(1);
        session = session.addRound(round1);
        session = session.startRound(PositiveInt.fromPrimitives(1));

        // Track room states as participants join
        const roomStates: Array<{
          joining: number;
          filling: number;
          ready: number;
          closed: number;
        }> = [];

        // Join participants progressively
        for (let i = 0; i < participantCount; i++) {
          const participantId = ParticipantId.fromPrimitives(participantIds[i]);
          session = session.onJoin(participantId, at);

          // Record room states every 5 participants
          if ((i + 1) % 5 === 0 || i === participantCount - 1) {
            const fillingRooms = session.getFillingRooms().length;
            const readyRooms = session.getReadyRooms().length;
            const totalBreakoutRooms = session.rooms.length - 1; // Exclude main room

            roomStates.push({
              joining: i + 1,
              filling: fillingRooms,
              ready: readyRooms,
              closed: totalBreakoutRooms - fillingRooms - readyRooms,
            });
          }
        }

        // Verify final state
        expect(session.rooms.length - 1).toBe(expectedBreakoutRooms); // Exclude main room
        expect(session.participants.length).toBe(participantCount);

        // Verify room progression - should have created rooms progressively
        expect(roomStates.length).toBeGreaterThan(0);
        expect(
          roomStates[roomStates.length - 1].filling +
            roomStates[roomStates.length - 1].ready,
        ).toBeGreaterThan(0);
      },
    );
  });

  describe('Round Start Redistribution', () => {
    it('redistributes 50 participants across breakout rooms when starting a round', () => {
      let session = createLiveSession();
      const participantIds = generateParticipantIds(50);
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      // Join all participants first (they'll be in main room)
      for (const id of participantIds) {
        const participantId = ParticipantId.fromPrimitives(id);
        session = session.onJoin(participantId, at);
      }

      // Verify all in main room initially
      const mainRoom = session.findRoomById(
        RoomId.fromPrimitives(validMainRoomId),
      );
      expect(mainRoom?.size).toBe(50);

      // Add and start a round
      const round1 = createRound(1);
      session = session.addRound(round1);
      session = session.startRound(PositiveInt.fromPrimitives(1));

      // Redistribute participants into breakout rooms
      session = session.distributeParticipantsIntoBreakoutRooms(at);

      // Verify redistribution occurred
      const breakoutRooms = session.rooms.filter(
        (room) => room.roomId.toPrimitives() !== validMainRoomId,
      );

      expect(breakoutRooms.length).toBe(13); // 12 rooms of 4, 1 of 2

      // Verify participants are distributed across breakout rooms
      const totalInBreakoutRooms = breakoutRooms.reduce(
        (sum, room) => sum + room.size,
        0,
      );
      expect(totalInBreakoutRooms).toBe(50);
      expect(session.rooms.length).toBe(breakoutRooms.length + 1); // Main room + breakout rooms

      // Verify main room is empty
      const mainRoomAfter = session.findRoomById(
        RoomId.fromPrimitives(validMainRoomId),
      );
      expect(mainRoomAfter?.size).toBe(0); // Empty
    });
  });

  describe('End Round - Return All to Main Room', () => {
    it('returns all 50 participants to main room when ending a round', () => {
      let session = createLiveSession();
      const participantIds = generateParticipantIds(50);
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      // Join participants and start round (distributes to breakout rooms)
      for (const id of participantIds) {
        const participantId = ParticipantId.fromPrimitives(id);
        session = session.onJoin(participantId, at);
      }

      const round1 = createRound(1);
      session = session.addRound(round1);
      session = session.startRound(PositiveInt.fromPrimitives(1));

      // Redistribute participants into breakout rooms
      session = session.distributeParticipantsIntoBreakoutRooms(at);

      // Verify participants are in breakout rooms
      const breakoutRoomsBefore = session.rooms.filter(
        (room) => room.roomId.toPrimitives() !== validMainRoomId,
      );
      const totalInBreakoutBefore = breakoutRoomsBefore.reduce(
        (sum, room) => sum + room.size,
        0,
      );
      expect(totalInBreakoutBefore).toBe(50);

      // End the round
      session = session.endCurrentRound();

      // Return all participants to main room
      session = session.returnAllParticipantsToMain(at);

      // Verify all participants returned to main room
      const mainRoom = session.findRoomById(
        RoomId.fromPrimitives(validMainRoomId),
      );
      expect(mainRoom?.size).toBe(50);

      // Verify breakout rooms are empty but still exist
      const breakoutRoomsAfter = session.rooms.filter(
        (room) => room.roomId.toPrimitives() !== validMainRoomId,
      );
      expect(breakoutRoomsAfter.length).toBeGreaterThan(0); // Rooms still exist
      breakoutRoomsAfter.forEach((room) => {
        expect(room.size).toBe(0); // But are empty
      });
    });
  });

  describe('Reconnection with Seat Reservation', () => {
    it.each([
      { disconnectedCount: 5, description: 'few participants' },
      { disconnectedCount: 15, description: 'many participants' },
      { disconnectedCount: 25, description: 'half the participants' },
    ])(
      'handles reconnection of $description during active round',
      ({ disconnectedCount }) => {
        let session = createLiveSession();
        const participantIds = generateParticipantIds(50);
        const at = Instant.fromPrimitives(baseTimestamp + 1000);

        // Join all participants and start round
        for (const id of participantIds) {
          const participantId = ParticipantId.fromPrimitives(id);
          session = session.onJoin(participantId, at);
        }

        const round1 = createRound(1);
        session = session.addRound(round1);
        session = session.startRound(PositiveInt.fromPrimitives(1));

        // Record initial room assignments
        const initialAssignments = new Map<string, string>();
        session.participants.forEach((participant) => {
          if (participant.roomId) {
            initialAssignments.set(
              participant.participantId,
              participant.roomId,
            );
          }
        });

        // Disconnect some participants (soft leave)
        const disconnectedIds = participantIds.slice(0, disconnectedCount);
        for (const id of disconnectedIds) {
          const participantId = ParticipantId.fromPrimitives(id);
          session = session.onSoftLeave(participantId, at);
        }

        // Verify seats are reserved
        const roomsWithReservations = session.rooms.filter((room) =>
          room.seats.some((seat) => seat.isReservedForReconnect),
        );
        expect(roomsWithReservations.length).toBeGreaterThan(0);

        // Reconnect participants
        for (const id of disconnectedIds) {
          const participantId = ParticipantId.fromPrimitives(id);
          session = session.onJoin(participantId, at);
        }

        // Verify participants returned to their original rooms
        let correctReconnections = 0;
        session.participants.forEach((participant) => {
          const participantIdStr = participant.participantId;
          const currentRoomId = participant.roomId;
          const originalRoomId = initialAssignments.get(participantIdStr);

          if (
            disconnectedIds.includes(participantIdStr) &&
            currentRoomId === originalRoomId
          ) {
            correctReconnections++;
          }
        });

        expect(correctReconnections).toBe(disconnectedCount);
        expect(session.participants.length).toBe(50); // All participants back
      },
    );
  });

  describe('Breakout Room Rotation', () => {
    it('handles multiple round rotations maintaining room structure with 50 participants', () => {
      let session = createLiveSession();
      const participantIds = generateParticipantIds(50);
      const at = Instant.fromPrimitives(baseTimestamp + 1000);
      const round1 = createRound(1);
      session = session.addRound(round1);
      const round2 = createRound(2);
      session = session.addRound(round2);

      // Join all participants
      for (const id of participantIds) {
        const participantId = ParticipantId.fromPrimitives(id);
        session = session.onJoin(participantId, at);
      }

      // Start Round 1
      session = session.startRound(PositiveInt.fromPrimitives(1));

      // Redistribute participants into breakout rooms
      session = session.distributeParticipantsIntoBreakoutRooms(at);

      const breakoutRoomsAfterRound1 = session.rooms.filter(
        (room) => room.roomId.toPrimitives() !== validMainRoomId,
      );
      const round1RoomCount = breakoutRoomsAfterRound1.length;

      // End round 1
      session = session.endCurrentRound();

      // Start Round 2 - should reuse existing breakout rooms

      // Rotate breakout rooms
      session = session.rotateBreakouts(at);

      session = session.startRound(PositiveInt.fromPrimitives(2));

      const breakoutRoomsAfterRound2 = session.rooms.filter(
        (room) => room.roomId.toPrimitives() !== validMainRoomId,
      );

      // Should reuse existing rooms, not create new ones
      expect(breakoutRoomsAfterRound2.length).toBe(round1RoomCount);

      // Verify rotation occurred (participants in different arrangements)
      const totalInBreakoutRooms = breakoutRoomsAfterRound2.reduce(
        (sum, room) => sum + room.size,
        0,
      );
      expect(totalInBreakoutRooms).toBe(50);

      // End round 2
      session = session.endCurrentRound();
      const mainRoomAfterEnd2 = session.findRoomById(
        RoomId.fromPrimitives(validMainRoomId),
      );
      expect(mainRoomAfterEnd2?.size).toBe(0);

      // Verify all reserved seats are released
      const roomsWithReservations = session.rooms.filter((room) =>
        room.seats.some((seat) => seat.isReservedForReconnect),
      );
      expect(roomsWithReservations.length).toBe(0);
    });

    it.each([
      {
        name: 'tiny: 6 total / 0 disc / 0 reconn',
        total: 6,
        disconnect: 0,
        reconnect: 0,
      },
      {
        name: 'small: 10 total / 2 disc / 1 reconn',
        total: 10,
        disconnect: 2,
        reconnect: 1,
      },
      {
        name: 'medium: 24 total / 5 disc / 2 reconn',
        total: 24,
        disconnect: 5,
        reconnect: 2,
      },
      {
        name: 'large: 50 total / 5 disc / 2 reconn',
        total: 50,
        disconnect: 5,
        reconnect: 2,
      },
      // TODO: Handle stress cases (probably we are having singleton)
      // {
      //   name: 'stress: 50 total / 12 disc / 7 reconn',
      //   total: 50,
      //   disconnect: 12,
      //   reconnect: 7,
      // },
      {
        name: 'edge: odd 31 total / 3 disc / 0 reconn',
        total: 31,
        disconnect: 3,
        reconnect: 0,
      },
      // TODO: Handle most disconnect (probably we are having singleton)
      // {
      //   name: 'edge: most disconnect 20 total / 18 disc / 5 reconn',
      //   total: 20,
      //   disconnect: 18,
      //   reconnect: 5,
      // },
    ])('$name', ({ total, disconnect, reconnect }) => {
      let session = createLiveSession();
      const ids = generateParticipantIds(total);
      const at = Instant.fromPrimitives(1_640_995_201_000); // fixed time for determinism
      session = session.addRound(createRound(1));
      session = session.addRound(createRound(2));

      // 1) Everyone joins
      for (const id of ids) {
        session = session.onJoin(ParticipantId.fromPrimitives(id), at);
      }

      // 2) Round 1

      session = session.startRound(PositiveInt.fromPrimitives(1));

      // 3) Distribute into breakouts per your allocator
      session = session.distributeParticipantsIntoBreakoutRooms(at);

      // 4) Some disconnect during round 1 (becomes RESERVED_FOR_RECONNECT)
      const disconnectedIds = ids.slice(0, disconnect);
      for (const id of disconnectedIds) {
        session = session.onSoftLeave(ParticipantId.fromPrimitives(id), at);
      }

      // Sanity: we have "disconnect" reservations
      const reservedCountBefore = session.rooms
        .filter((r) => r.roomId.toPrimitives() !== validMainRoomId)
        .reduce((sum, r) => sum + r.reservedSeats.length, 0);
      expect(reservedCountBefore).toBe(disconnect);

      // 5) End round 1 (grace begins)
      session = session.endCurrentRound();

      // 6) Rotate (policy expectation: reserved are NOT re-seated; the post-rotation headcount should be total - disconnect)
      session = session.rotateBreakouts(at);

      const headcountAfterRotation = session.rooms
        .filter((r) => r.roomId.toPrimitives() !== validMainRoomId)
        .reduce((sum, r) => sum + r.size, 0);

      expect(headcountAfterRotation).toBe(total - disconnect);

      // 7) Start round 2 (should redistribute only active participants)
      session = session.startRound(PositiveInt.fromPrimitives(2));

      // 8) No singletons policy (or empty rooms ok):
      const allRoomsOk = session.rooms
        .filter((r) => r.roomId.toPrimitives() !== validMainRoomId)
        .every((r) => r.size === 0 || r.size >= r.config.minSeats );
      expect(allRoomsOk).toBe(true);

      // 9) Some of the previously disconnected reconnect during round 2
      const reconnectingIds = disconnectedIds.slice(0, reconnect);
      for (const id of reconnectingIds) {
        session = session.onJoin(ParticipantId.fromPrimitives(id), at);
      }

      // 10) Verify total active (seated) participants now
      const active = session.participants.filter((p) => p.roomId).length;
      expect(active).toBe(total - disconnect + reconnect);
    });

  });
});
