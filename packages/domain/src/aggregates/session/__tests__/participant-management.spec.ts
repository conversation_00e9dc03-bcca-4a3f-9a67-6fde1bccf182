import { Session } from '../session.aggregate';
import { SessionId } from '../../../value-objects/sessions/session-id/session-id.vo';
import { SessionConfig } from '../../../value-objects/sessions/session-config/session-config.vo';
import { SessionConfigPrimitives } from '../../../value-objects/sessions/contracts/session-config.type';
import { SessionMode } from '../../../value-objects/sessions/contracts/session-mode.enum';
import { RoomId } from '../../../value-objects/rooms/room-id/room-id.vo';
import { ParticipantId } from '../../../value-objects/participants/participant-id/participant-id.vo';
import { ParticipantRole } from '../../../value-objects/participants/contracts/participant-role.enum';
import { Uuid } from '../../../primitives/uuid/uuid.primitive';
import { Instant } from '../../../primitives/instant/instant.primitive';
import { Session<PERSON>tateError, SessionCapacityError, SessionParticipantNotFoundError } from '../session.errors';
import { LateJoinAllocationMode } from '../../../policies/late-join/types/late-join-allocation-mode.enum';
import { AllocationStrategy } from '../../../policies/autopilot/types/allocation-strategy.enum';

describe('Session: Participant Management', () => {
  const validSessionId = '550e8400-e29b-41d4-a716-************';
  const validAdminId = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
  const validMainRoomId = '6ba7b810-9dad-41d1-80b4-00c04fd430c8';
  const validParticipantId1 = '123e4567-e89b-42d3-a456-************';
  const baseTimestamp = 1640995200000; // 2022-01-01 00:00:00 UTC

  const validSessionConfig: SessionConfigPrimitives = {
    scheduledStartAt: baseTimestamp + 300000,
    estimatedDurationMs: 3600000,
    defaultRoomConfig: {
      minSeats: 2,
      maxSeats: 8,
      avoidSingleton: true,
      disconnectionPolicy: { holdSeatForMs: 30000 },
    },
    maxParticipants: 10, // Must be >= maxSeats (8)
    defaultRoundDurationMs: 900000,
    autopilotPolicy: {
      allocationStategy: AllocationStrategy.ROUND_ROBIN,
    },
    lobbyAdmissionPolicy: {
      allowEarlyJoinMs: 300000,
      requireHostPresent: true
    },
    disconnectionPolicy: {
      holdSeatForMs: 120000,
    },
    lateJoinPolicy: {
      allocationMode: LateJoinAllocationMode.BEST_FIT
    },
    mode: SessionMode.HOSTED,
  };

  function createTestSession(overrideConfig?: Partial<SessionConfigPrimitives>): Session {
    const sessionId = SessionId.fromPrimitives(validSessionId);
    const config = SessionConfig.fromPrimitives({...validSessionConfig, ...overrideConfig});
    const adminId = Uuid.fromPrimitives(validAdminId);
    const mainRoomId = RoomId.fromPrimitives(validMainRoomId);

    return Session.create(sessionId, config, adminId, mainRoomId);
  }

  function createRunningSession(overrideConfig?: Partial<SessionConfigPrimitives>): Session {
    return createTestSession({...validSessionConfig, ...overrideConfig}).startSession();
  }

  describe('addParticipant', () => {
    it('adds participant to session', () => {
      const session = createTestSession();
      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      const updatedSession = session.addParticipant(participantId, at);

      expect(updatedSession.participants).toHaveLength(1);
      expect(updatedSession.participants[0].participantId).toBe(validParticipantId1);
      expect(updatedSession.participants[0].tags.role).toBe(ParticipantRole.MEMBER);
    });

    it('is idempotent when participant already exists', () => {
      const session = createTestSession();
      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      const sessionWithParticipant = session.addParticipant(participantId, at);
      const sessionAgain = sessionWithParticipant.addParticipant(participantId, at);

      expect(sessionAgain.participants).toHaveLength(1);
      expect(sessionAgain).toBe(sessionWithParticipant); // Should return same instance
    });
  });

  describe('onJoin', () => {
    it('throws SessionStateError when session is not RUNNING', () => {
      const session = createTestSession();
      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      expect(() => session.onJoin(participantId, at)).toThrow(SessionStateError);
    });

    it('throws SessionCapacityError when session is at capacity', () => {
      let session = createRunningSession({ maxParticipants: 8 });
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      // Fill room to capacity (8 seats)
      const participantIds = [
        '123e4567-e89b-42d3-a456-************',
        '987fcdeb-51a2-43d1-9f12-345678901234',
        'abcdef12-3456-4890-abcd-ef1234567890',
        '11111111-2222-4333-8444-555555555555',
        '66666666-7777-4888-9999-aaaaaaaaaaaa',
        'bbbbbbbb-cccc-4ddd-aeee-ffffffffffff',
        '12345678-1234-4234-8234-123456789012',
        '87654321-4321-4321-a321-210987654321'
      ];

      // Fill all 8 seats
      for (const id of participantIds) {
        const participant = ParticipantId.fromPrimitives(id);
        session = session.onJoin(participant, at);
      }

      // Try to add one more - should fail due to room capacity
      const extraParticipant = ParticipantId.fromPrimitives('ffffffff-ffff-4fff-afff-ffffffffffff');
      expect(() => session.onJoin(extraParticipant, at)).toThrow(SessionCapacityError);
    });

    it('adds new participant and seats them in main room when no round is active', () => {
      const session = createRunningSession();
      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      const updatedSession = session.onJoin(participantId, at);

      expect(updatedSession.participants).toHaveLength(1);
      expect(updatedSession.participants[0].participantId).toBe(validParticipantId1);
      expect(updatedSession.participants[0].isInRoom).toBe(true);
      expect(updatedSession.participants[0].roomId).toBe(validMainRoomId);

      // Check that participant is seated in main room
      const mainRoomId = RoomId.fromPrimitives(validMainRoomId);
      const mainRoom = updatedSession.findRoomById(mainRoomId);
      expect(mainRoom!.size).toBe(1);
      expect(mainRoom!.occupiedSeats[0].currentParticipantId).toBe(validParticipantId1);
    });

    it('handles reconnection of existing participant', () => {
      let session = createRunningSession();
      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      // First join
      session = session.onJoin(participantId, at);
      expect(session.participants).toHaveLength(1);

      // Simulate soft leave
      const leaveAt = Instant.fromPrimitives(baseTimestamp + 2000);
      session = session.onSoftLeave(participantId, leaveAt);

      expect(session.participants[0].hasLeft).toBe(true);

      // Rejoin
      const rejoinAt = Instant.fromPrimitives(baseTimestamp + 3000);
      const rejoinedSession = session.onJoin(participantId, rejoinAt);

      expect(rejoinedSession.participants).toHaveLength(1);
      expect(rejoinedSession.participants[0].participantId).toBe(validParticipantId1);
    });
  });

  describe('seatParticipant', () => {
    it('throws SessionStateError when session is not RUNNING', () => {
      const session = createTestSession();
      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      // Add participant first
      const sessionWithParticipant = session.addParticipant(participantId, at);

      expect(() => sessionWithParticipant.seatParticipant(participantId, at)).toThrow(SessionStateError);
    });

    it('throws SessionParticipantNotFoundError when participant not in session', () => {
      const session = createRunningSession();
      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      expect(() => session.seatParticipant(participantId, at)).toThrow(SessionParticipantNotFoundError);
    });

    it('seats participant in main room when no round is active', () => {
      let session = createRunningSession();
      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      // Add participant first
      session = session.addParticipant(participantId, at);

      const seatedSession = session.seatParticipant(participantId, at);

      const participant = seatedSession.findParticipantById(participantId);
      expect(participant!.isInRoom).toBe(true);
      expect(participant!.roomId).toBe(validMainRoomId);

      const mainRoomId = RoomId.fromPrimitives(validMainRoomId);
      const mainRoom = seatedSession.findRoomById(mainRoomId);
      expect(mainRoom!.size).toBe(1);
    });
  });

  describe('onSoftLeave', () => {
    it('handles participant leaving gracefully', () => {
      let session = createRunningSession();
      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const joinAt = Instant.fromPrimitives(baseTimestamp + 1000);
      const leaveAt = Instant.fromPrimitives(baseTimestamp + 2000);

      // Join first
      session = session.onJoin(participantId, joinAt);
      expect(session.participants[0].hasLeft).toBe(false);

      // Soft leave
      const leftSession = session.onSoftLeave(participantId, leaveAt);

      expect(leftSession.participants[0].hasLeft).toBe(true);
      expect(leftSession.participants[0].leaveTime).toBe(baseTimestamp + 2000);
    });

    it('reserves seat for reconnection when participant has a seat', () => {
      let session = createRunningSession();
      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const joinAt = Instant.fromPrimitives(baseTimestamp + 1000);
      const leaveAt = Instant.fromPrimitives(baseTimestamp + 2000);

      // Join and get seated
      session = session.onJoin(participantId, joinAt);
      
      // Verify participant is seated
      const mainRoomId = RoomId.fromPrimitives(validMainRoomId);
      const mainRoom = session.findRoomById(mainRoomId);
      expect(mainRoom!.size).toBe(1);
      expect(mainRoom!.occupiedSeats).toHaveLength(1);
      expect(mainRoom!.reservedSeats).toHaveLength(0);

      // Soft leave
      const leftSession = session.onSoftLeave(participantId, leaveAt);

      // Check that seat is reserved
      const mainRoomAfterLeave = leftSession.findRoomById(mainRoomId);
      expect(mainRoomAfterLeave!.size).toBe(1); // Still counts toward size
      expect(mainRoomAfterLeave!.occupiedSeats).toHaveLength(0);
      expect(mainRoomAfterLeave!.reservedSeats).toHaveLength(1);
      expect(mainRoomAfterLeave!.reservedSeats[0].currentParticipantId).toBe(validParticipantId1);
    });

    it('is no-op when participant does not exist', () => {
      const session = createRunningSession();
      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      const result = session.onSoftLeave(participantId, at);

      expect(result).toBe(session); // Should return same instance
    });
  });

  describe('assignParticipantToRoom', () => {
    it('moves participant from main room to another room', () => {
      let session = createRunningSession();
      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const joinAt = Instant.fromPrimitives(baseTimestamp + 1000);
      const moveAt = Instant.fromPrimitives(baseTimestamp + 2000);

      // Join and get seated in main room
      session = session.onJoin(participantId, joinAt);

      // Create a breakout room
      const breakoutRoomId = RoomId.fromPrimitives('12345678-1234-4567-8901-234567890123');
      session = session.createBreakoutRoom(breakoutRoomId, moveAt);

      const movedSession = session.assignParticipantToRoom(participantId, breakoutRoomId, moveAt);

      const participant = movedSession.findParticipantById(participantId);
      expect(participant!.roomId).toBe(breakoutRoomId.toPrimitives());

      const breakoutRoom = movedSession.findRoomById(breakoutRoomId);
      expect(breakoutRoom!.size).toBe(1);

      const mainRoomId = RoomId.fromPrimitives(validMainRoomId);
      const mainRoom = movedSession.findRoomById(mainRoomId);
      expect(mainRoom!.size).toBe(0);
    });
  });

  describe('restoreReservedSeat', () => {
    it('restores participant to their reserved seat', () => {
      let session = createRunningSession();
      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const joinAt = Instant.fromPrimitives(baseTimestamp + 1000);
      const leaveAt = Instant.fromPrimitives(baseTimestamp + 2000);
      const restoreAt = Instant.fromPrimitives(baseTimestamp + 3000);

      // Join, leave, then restore
      session = session.onJoin(participantId, joinAt);
      session = session.onSoftLeave(participantId, leaveAt);

      const restoredSession = session.restoreReservedSeat(participantId, restoreAt);

      const participant = restoredSession.findParticipantById(participantId);
      expect(participant!.isInRoom).toBe(true);
      expect(participant!.roomId).toBe(validMainRoomId);

      const mainRoomId = RoomId.fromPrimitives(validMainRoomId);
      const mainRoom = restoredSession.findRoomById(mainRoomId);
      expect(mainRoom!.size).toBe(1);
      expect(mainRoom!.occupiedSeats).toHaveLength(1);
      expect(mainRoom!.reservedSeats).toHaveLength(0);
    });
  });
});
