import { Session } from '../session.aggregate';
import { SessionId } from '../../../value-objects/sessions/session-id/session-id.vo';
import { SessionConfig } from '../../../value-objects/sessions/session-config/session-config.vo';
import { SessionConfigPrimitives } from '../../../value-objects/sessions/contracts/session-config.type';
import { SessionMode } from '../../../value-objects/sessions/contracts/session-mode.enum';
import { RoomId } from '../../../value-objects/rooms/room-id/room-id.vo';
import { ParticipantId } from '../../../value-objects/participants/participant-id/participant-id.vo';
import { ParticipantRole } from '../../../value-objects/participants/contracts/participant-role.enum';
import { Uuid } from '../../../primitives/uuid/uuid.primitive';
import { Instant } from '../../../primitives/instant/instant.primitive';
import { SessionInvariantError } from '../session.errors';
import { LateJoinAllocationMode } from '../../../policies/late-join/types/late-join-allocation-mode.enum';
import { AllocationStrategy } from '../../../policies/autopilot/types/allocation-strategy.enum';

describe('Session: Host Management', () => {
  const validSessionId = '550e8400-e29b-41d4-a716-************';
  const validAdminId = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
  const validMainRoomId = '6ba7b810-9dad-41d1-80b4-00c04fd430c8';
  const validParticipantId1 = '123e4567-e89b-42d3-a456-************';
  const validParticipantId2 = '987fcdeb-51a2-43d1-9f12-************';
  const validParticipantId3 = 'abcdef12-3456-4890-abcd-ef1234567890';
  const baseTimestamp = 1640995200000; // 2022-01-01 00:00:00 UTC

  const validSessionConfig: SessionConfigPrimitives = {
    scheduledStartAt: baseTimestamp + 300000,
    estimatedDurationMs: 3600000,
    defaultRoomConfig: {
      minSeats: 2,
      maxSeats: 8,
      avoidSingleton: true,
      disconnectionPolicy: { holdSeatForMs: 30000 },
    },
    maxParticipants: 50,
    defaultRoundDurationMs: 900000,
    autopilotPolicy: {
      allocationStategy: AllocationStrategy.ROUND_ROBIN,
    },
    lobbyAdmissionPolicy: {
      allowEarlyJoinMs: 300000,
      requireHostPresent: true
    },
    disconnectionPolicy: {
      holdSeatForMs: 120000,
    },
    lateJoinPolicy: {
      allocationMode: LateJoinAllocationMode.BEST_FIT
    },
    mode: SessionMode.HOSTED,
  };

  function createTestSession(): Session {
    const sessionId = SessionId.fromPrimitives(validSessionId);
    const config = SessionConfig.fromPrimitives(validSessionConfig);
    const adminId = Uuid.fromPrimitives(validAdminId);
    const mainRoomId = RoomId.fromPrimitives(validMainRoomId);

    return Session.create(sessionId, config, adminId, mainRoomId);
  }

  function createSessionWithParticipants(): Session {
    let session = createTestSession();
    const at = Instant.fromPrimitives(baseTimestamp + 1000);

    // Add participants with different roles
    const participant1 = ParticipantId.fromPrimitives(validParticipantId1);
    const participant2 = ParticipantId.fromPrimitives(validParticipantId2);
    const participant3 = ParticipantId.fromPrimitives(validParticipantId3);

    session = session.addParticipant(participant1, at, ParticipantRole.MEMBER);
    session = session.addParticipant(participant2, at, ParticipantRole.HOST);
    session = session.addParticipant(participant3, at, ParticipantRole.MEMBER);

    return session;
  }

  describe('Initial state', () => {
    it('creates session without host initially', () => {
      const session = createTestSession();

      expect(session.hasHost).toBe(false);
    });
  });

  describe('setHost', () => {
    it('sets host when participant has HOST role', () => {
      const session = createSessionWithParticipants();
      const hostId = ParticipantId.fromPrimitives(validParticipantId2);

      const sessionWithHost = session.setHost(hostId);

      expect(sessionWithHost.hasHost).toBe(true);
      expect(sessionWithHost.toPrimitives().hostId).toBe(validParticipantId2);
    });

    it('is idempotent when setting same host', () => {
      const session = createSessionWithParticipants();
      const hostId = ParticipantId.fromPrimitives(validParticipantId2);

      const sessionWithHost = session.setHost(hostId);
      const sessionAgain = sessionWithHost.setHost(hostId);

      expect(sessionAgain.hasHost).toBe(true);
      expect(sessionAgain.toPrimitives().hostId).toBe(validParticipantId2);
      expect(sessionAgain).toBe(sessionWithHost); // Should return same instance
    });

    it('changes host when setting different host', () => {
      let session = createSessionWithParticipants();
      const hostId1 = ParticipantId.fromPrimitives(validParticipantId2);

      // Set first host
      session = session.setHost(hostId1);
      expect(session.toPrimitives().hostId).toBe(validParticipantId2);

      // Add another participant with HOST role
      const at = Instant.fromPrimitives(baseTimestamp + 2000);
      const newHostId = ParticipantId.fromPrimitives(Uuid.generate());
      session = session.addParticipant(newHostId, at, ParticipantRole.HOST);

      // Change host
      const sessionWithNewHost = session.setHost(newHostId);
      expect(sessionWithNewHost.toPrimitives().hostId).toBe(newHostId.toPrimitives());
    });

    it('throws SessionInvariantError when participant is not in session', () => {
      const session = createSessionWithParticipants();
      const nonExistentParticipant = ParticipantId.fromPrimitives(Uuid.generate());

      expect(() => session.setHost(nonExistentParticipant)).toThrow(SessionInvariantError);
    });

    it('throws SessionInvariantError when participant does not have HOST role', () => {
      const session = createSessionWithParticipants();
      const memberParticipant = ParticipantId.fromPrimitives(validParticipantId1); // Has MEMBER role

      expect(() => session.setHost(memberParticipant)).toThrow(SessionInvariantError);
    });
  });

  describe('Host role validation', () => {
    it('validates that only participants with HOST role can be set as host', () => {
      let session = createTestSession();
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      // Add participants with different roles
      const memberParticipant = ParticipantId.fromPrimitives(validParticipantId1);
      const hostParticipant = ParticipantId.fromPrimitives(validParticipantId2);

      session = session.addParticipant(memberParticipant, at, ParticipantRole.MEMBER);
      session = session.addParticipant(hostParticipant, at, ParticipantRole.HOST);

      // Should succeed for HOST role
      const sessionWithHost = session.setHost(hostParticipant);
      expect(sessionWithHost.hasHost).toBe(true);

      // Should fail for MEMBER role
      expect(() => session.setHost(memberParticipant)).toThrow(SessionInvariantError);
    });

    it('allows multiple participants with HOST role but only one can be the active host', () => {
      let session = createTestSession();
      const at = Instant.fromPrimitives(baseTimestamp + 1000);

      // Add multiple participants with HOST role
      const hostParticipant1 = ParticipantId.fromPrimitives(validParticipantId1);
      const hostParticipant2 = ParticipantId.fromPrimitives(validParticipantId2);

      session = session.addParticipant(hostParticipant1, at, ParticipantRole.HOST);
      session = session.addParticipant(hostParticipant2, at, ParticipantRole.HOST);

      // Set first host
      session = session.setHost(hostParticipant1);
      expect(session.toPrimitives().hostId).toBe(validParticipantId1);

      // Change to second host
      session = session.setHost(hostParticipant2);
      expect(session.toPrimitives().hostId).toBe(validParticipantId2);

      // Only one host should be active
      expect(session.hasHost).toBe(true);
    });
  });

  describe('Host persistence in session operations', () => {
    it('preserves host through state transitions', () => {
      let session = createSessionWithParticipants();
      const hostId = ParticipantId.fromPrimitives(validParticipantId2);

      // Set host
      session = session.setHost(hostId);
      expect(session.toPrimitives().hostId).toBe(validParticipantId2);

      // Start session
      session = session.startSession();
      expect(session.toPrimitives().hostId).toBe(validParticipantId2);

      // Pause session
      session = session.pauseSession();
      expect(session.toPrimitives().hostId).toBe(validParticipantId2);

      // Resume session
      session = session.resumeSession();
      expect(session.toPrimitives().hostId).toBe(validParticipantId2);

      // Complete session
      session = session.completeSession();
      expect(session.toPrimitives().hostId).toBe(validParticipantId2);
    });

    it('preserves host through participant operations', () => {
      let session = createSessionWithParticipants();
      const hostId = ParticipantId.fromPrimitives(validParticipantId2);
      const at = Instant.fromPrimitives(baseTimestamp + 2000);

      // Set host
      session = session.setHost(hostId);
      expect(session.toPrimitives().hostId).toBe(validParticipantId2);

      // Start session and have host join
      session = session.startSession();
      session = session.onJoin(hostId, at);
      expect(session.toPrimitives().hostId).toBe(validParticipantId2);

      // Host leaves and rejoins
      session = session.onSoftLeave(hostId, at);
      expect(session.toPrimitives().hostId).toBe(validParticipantId2);

      session = session.onJoin(hostId, at);
      expect(session.toPrimitives().hostId).toBe(validParticipantId2);
    });

    it('preserves host through room operations', () => {
      let session = createSessionWithParticipants();
      const hostId = ParticipantId.fromPrimitives(validParticipantId2);
      const at = Instant.fromPrimitives(baseTimestamp + 2000);

      // Set host and start session
      session = session.setHost(hostId);
      session = session.startSession();
      session = session.onJoin(hostId, at);

      // Create breakout room and move host
      const breakoutRoomId = RoomId.fromPrimitives('12345678-1234-4567-8901-234567890123');
      session = session.createBreakoutRoom(breakoutRoomId, at);
      // session = session.assignParticipantToRoom(hostId, breakoutRoomId, at);

      expect(session.toPrimitives().hostId).toBe(validParticipantId2);

      // Return to main room
      session = session.returnAllParticipantsToMain(at);
      expect(session.toPrimitives().hostId).toBe(validParticipantId2);
    });
  });

  describe('Roundtrip persistence', () => {
    it('preserves host information in toPrimitives/fromPrimitives roundtrip', () => {
      let session = createSessionWithParticipants();
      const hostId = ParticipantId.fromPrimitives(validParticipantId2);

      // Set host
      session = session.setHost(hostId);

      // Roundtrip through primitives
      const primitives = session.toPrimitives();
      expect(primitives.hostId).toBe(validParticipantId2);

      const restoredSession = Session.fromPrimitives(primitives);
      expect(restoredSession.hasHost).toBe(true);
      expect(restoredSession.toPrimitives().hostId).toBe(validParticipantId2);
    });

    it('handles session without host in roundtrip', () => {
      const session = createSessionWithParticipants();

      // No host set
      expect(session.hasHost).toBe(false);

      // Roundtrip through primitives
      const primitives = session.toPrimitives();
      expect(primitives.hostId).toBeUndefined();

      const restoredSession = Session.fromPrimitives(primitives);
      expect(restoredSession.hasHost).toBe(false);
      expect(restoredSession.toPrimitives().hostId).toBeUndefined();
    });
  });
});
