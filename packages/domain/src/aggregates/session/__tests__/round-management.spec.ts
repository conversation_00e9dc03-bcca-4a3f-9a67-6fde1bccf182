import { Session } from '../session.aggregate';
import { SessionId } from '../../../value-objects/sessions/session-id/session-id.vo';
import { SessionConfig } from '../../../value-objects/sessions/session-config/session-config.vo';
import { SessionConfigPrimitives } from '../../../value-objects/sessions/contracts/session-config.type';
import { SessionMode } from '../../../value-objects/sessions/contracts/session-mode.enum';
import { RoomId } from '../../../value-objects/rooms/room-id/room-id.vo';
import { Uuid } from '../../../primitives/uuid/uuid.primitive';
import { PositiveInt } from '../../../primitives/positive-int/positive-int.primitive';
import { Round } from '../../../value-objects/sessions/round/round.entity';
import { RoundKind } from '../../../value-objects/sessions/round/contracts/round-kind.enum';
import { SessionStateError, SessionInvariantError } from '../session.errors';
import { LateJoinAllocationMode } from '../../../policies/late-join/types/late-join-allocation-mode.enum';
import { AllocationStrategy } from '../../../policies/autopilot/types/allocation-strategy.enum';

describe('Session: Round Management', () => {
  const validSessionId = '550e8400-e29b-41d4-a716-************';
  const validAdminId = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
  const validMainRoomId = '6ba7b810-9dad-41d1-80b4-00c04fd430c8';
  const baseTimestamp = 1640995200000; // 2022-01-01 00:00:00 UTC

  const validSessionConfig: SessionConfigPrimitives = {
    scheduledStartAt: baseTimestamp + 300000,
    estimatedDurationMs: 3600000,
    defaultRoomConfig: {
      minSeats: 2,
      maxSeats: 8,
      avoidSingleton: true,
      disconnectionPolicy: { holdSeatForMs: 30000 },
    },
    maxParticipants: 50,
    defaultRoundDurationMs: 900000,
    autopilotPolicy: {
      allocationStategy: AllocationStrategy.ROUND_ROBIN,
    },
    lobbyAdmissionPolicy: {
      allowEarlyJoinMs: 300000,
      requireHostPresent: true,
    },
    disconnectionPolicy: {
      holdSeatForMs: 120000,
    },
    lateJoinPolicy: {
      allocationMode: LateJoinAllocationMode.BEST_FIT,
    },
    mode: SessionMode.HOSTED,
  };

  function createTestSession(): Session {
    const sessionId = SessionId.fromPrimitives(validSessionId);
    const config = SessionConfig.fromPrimitives(validSessionConfig);
    const adminId = Uuid.fromPrimitives(validAdminId);
    const mainRoomId = RoomId.fromPrimitives(validMainRoomId);

    return Session.create(sessionId, config, adminId, mainRoomId);
  }

  function createRound(
    roundNo: number,
    questions: string[] = ['Default question'],
  ): Round {
    return Round.fromPrimitives({
      roundNo,
      kind: RoundKind.MAIN_TOPIC,
      durationMs: 900000,
      questions,
    });
  }

  describe('addRound', () => {
    it('adds round to session', () => {
      const session = createTestSession();
      const round = createRound(1);

      const updatedSession = session.addRound(round);

      expect(updatedSession.rounds).toHaveLength(1);
      expect(updatedSession.rounds[0].roundNo.toPrimitives()).toBe(1);
      expect(updatedSession.rounds[0].kind).toBe(RoundKind.MAIN_TOPIC);
    });

    it('is idempotent when round already exists', () => {
      const session = createTestSession();
      const round = createRound(1);

      const sessionWithRound = session.addRound(round);
      const sessionAgain = sessionWithRound.addRound(round);

      expect(sessionAgain.rounds).toHaveLength(1);
      expect(sessionAgain).toBe(sessionWithRound); // Should return same instance
    });

    it('sorts rounds by round number', () => {
      let session = createTestSession();
      const round3 = createRound(3);
      const round2 = createRound(2);
      const round1 = createRound(1);

      session = session.addRound(round3);
      session = session.addRound(round2);
      session = session.addRound(round1);

      expect(session.rounds).toHaveLength(3);
      expect(session.rounds[0].roundNo.toPrimitives()).toBe(1);
      expect(session.rounds[1].roundNo.toPrimitives()).toBe(2);
      expect(session.rounds[2].roundNo.toPrimitives()).toBe(3);
    });

    it('throws SessionInvariantError for non-consecutive round numbers', () => {
      let session = createTestSession();
      const round1 = createRound(1);
      const round3 = createRound(3); // Skip round 2

      session = session.addRound(round1);

      expect(() => session.addRound(round3)).toThrow(SessionInvariantError);
    });

    it('throws SessionStateError when session is COMPLETED', () => {
      const session = createTestSession();
      const runningSession = session.startSession();
      const completedSession = runningSession.completeSession();
      const round = createRound(1);

      expect(() => completedSession.addRound(round)).toThrow(SessionStateError);
    });

    it('throws SessionStateError when session is CANCELED', () => {
      const session = createTestSession();
      const canceledSession = session.cancelSession();
      const round = createRound(1);

      expect(() => canceledSession.addRound(round)).toThrow(SessionStateError);
    });
  });

  describe('startRound', () => {
    it('starts round and updates current round number', () => {
      let session = createTestSession();
      const round = createRound(1);

      session = session.addRound(round);
      const runningSession = session.startSession();

      const roundNo = PositiveInt.fromPrimitives(1);
      const sessionWithActiveRound = runningSession.startRound(roundNo);

      expect(sessionWithActiveRound.currentRound).toBeDefined();
      expect(sessionWithActiveRound.currentRound!.roundNo.toPrimitives()).toBe(
        1,
      );
    });

    it('throws SessionStateError when session is not RUNNING', () => {
      let session = createTestSession();
      const round = createRound(1);

      session = session.addRound(round);

      const roundNo = PositiveInt.fromPrimitives(1);
      expect(() => session.startRound(roundNo)).toThrow(SessionStateError);
    });

    it('throws SessionInvariantError when round number is out of bounds', () => {
      let session = createTestSession();
      const round = createRound(1);

      session = session.addRound(round);
      const runningSession = session.startSession();

      const invalidRoundNo = PositiveInt.fromPrimitives(5); // Only have 1 round
      expect(() => runningSession.startRound(invalidRoundNo)).toThrow(
        SessionInvariantError,
      );
    });

    it('throws SessionInvariantError when another round is already active', () => {
      let session = createTestSession();
      const round1 = createRound(1);
      const round2 = createRound(2);

      session = session.addRound(round1);
      session = session.addRound(round2);
      let runningSession = session.startSession();

      const roundNo1 = PositiveInt.fromPrimitives(1);
      runningSession = runningSession.startRound(roundNo1);

      const roundNo2 = PositiveInt.fromPrimitives(2);
      expect(() => runningSession.startRound(roundNo2)).toThrow(
        SessionInvariantError,
      );
    });
  });

  describe('endCurrentRound', () => {
    it('ends current round and resets current round number', () => {
      let session = createTestSession();
      const round = createRound(1);

      session = session.addRound(round);
      let runningSession = session.startSession();

      const roundNo = PositiveInt.fromPrimitives(1);
      runningSession = runningSession.startRound(roundNo);

      expect(runningSession.currentRound).toBeDefined();
      expect(runningSession.currentRound!.roundNo.toPrimitives()).toBe(1);

      const sessionWithEndedRound = runningSession.endCurrentRound();

      expect(sessionWithEndedRound.currentRound).toBeUndefined();
    });

    it('is no-op when no round is active', () => {
      const session = createTestSession();
      const runningSession = session.startSession();

      const result = runningSession.endCurrentRound();

      expect(result).toBe(runningSession); // Should return same instance
      expect(result.currentRound).toBeUndefined();
    });

    it('throws SessionStateError when session is not RUNNING', () => {
      const session = createTestSession();

      expect(() => session.endCurrentRound()).toThrow(SessionStateError);
    });

    it('throws SessionInvariantError when current round number is invalid', () => {
      let session = createTestSession();
      const round = createRound(1);

      session = session.addRound(round);
      let runningSession = session.startSession();

      const roundNo = PositiveInt.fromPrimitives(1);
      runningSession = runningSession.startRound(roundNo);

      // Create a session with invalid current round number via primitives
      const sessionPrimitives = runningSession.toPrimitives();
      sessionPrimitives.currentRoundNo = 99; // Invalid round number
      const corruptedSession = Session.fromPrimitives(sessionPrimitives);

      expect(() => corruptedSession.endCurrentRound()).toThrow(
        SessionInvariantError,
      );
    });
  });

  describe('currentRound getter', () => {
    it('returns undefined when no round is active', () => {
      const session = createTestSession();

      expect(session.currentRound).toBeUndefined();
    });

    it('returns current round when one is active', () => {
      let session = createTestSession();
      const round = createRound(1, ['What is your name?']);

      session = session.addRound(round);
      let runningSession = session.startSession();

      const roundNo = PositiveInt.fromPrimitives(1);
      runningSession = runningSession.startRound(roundNo);

      const currentRound = runningSession.currentRound;
      expect(currentRound).toBeDefined();
      expect(currentRound!.roundNo.toPrimitives()).toBe(1);
      expect(currentRound!.questions).toEqual(['What is your name?']);
    });
  });

  describe('Round progression scenarios', () => {
    it('supports multiple rounds in sequence', () => {
      let session = createTestSession();
      const round1 = createRound(1, ['Round 1 question']);
      const round2 = createRound(2, ['Round 2 question']);
      const round3 = createRound(3, ['Round 3 question']);

      // Add all rounds
      session = session.addRound(round1);
      session = session.addRound(round2);
      session = session.addRound(round3);

      let runningSession = session.startSession();

      // Start and end round 1
      runningSession = runningSession.startRound(
        PositiveInt.fromPrimitives(1),
      );
      expect(runningSession.currentRound!.roundNo.toPrimitives()).toBe(1);
      runningSession = runningSession.endCurrentRound();
      expect(runningSession.currentRound).toBeUndefined();

      // Start and end round 2
      runningSession = runningSession.startRound(
        PositiveInt.fromPrimitives(2),
      );
      expect(runningSession.currentRound!.roundNo.toPrimitives()).toBe(2);
      runningSession = runningSession.endCurrentRound();
      expect(runningSession.currentRound).toBeUndefined();

      // Start round 3
      runningSession = runningSession.startRound(
        PositiveInt.fromPrimitives(3),
      );
      expect(runningSession.currentRound!.roundNo.toPrimitives()).toBe(3);
    });
  });
});
