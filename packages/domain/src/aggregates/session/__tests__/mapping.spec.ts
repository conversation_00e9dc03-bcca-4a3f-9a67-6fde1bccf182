/* eslint-disable @typescript-eslint/no-explicit-any */
import { Session } from '../session.aggregate';
import { SessionPrimitives } from '../../../value-objects/sessions/contracts/session.type';
import { SessionConfigPrimitives } from '../../../value-objects/sessions/contracts/session-config.type';
import { SessionState } from '../../../value-objects/sessions/contracts/session.state.enum';
import { SessionMode } from '../../../value-objects/sessions/contracts/session-mode.enum';
import { RoomPrimitives } from '../../../value-objects/rooms/contracts/room.type';
import { ParticipantPresencePrimitives } from '../../../entities/participant-presence/contracts/presence.type';
import { RoundPrimitives } from '../../../value-objects/sessions/round/contracts/round.type';
import { RoundKind } from '../../../value-objects/sessions/round/contracts/round-kind.enum';
import { ParticipantRole } from '../../../value-objects/participants/contracts/participant-role.enum';
import { LateJoinAllocationMode } from '../../../policies/late-join/types/late-join-allocation-mode.enum';
import { AllocationStrategy } from '../../../policies/autopilot/types/allocation-strategy.enum';
import { PersistenceMappingError } from '../../../errors/persistence-mapping-error';

describe('Session: Mapping & Roundtrip', () => {
  const validSessionId = '550e8400-e29b-41d4-a716-446655440000';
  const validAdminId = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
  const validMainRoomId = '6ba7b810-9dad-41d1-80b4-00c04fd430c8';
  const validParticipantId1 = '123e4567-e89b-42d3-a456-426614174000';
  const validParticipantId2 = '987fcdeb-51a2-43d1-9f12-345678901234';
  const baseTimestamp = 1640995200000; // 2022-01-01 00:00:00 UTC

  const validSessionConfig: SessionConfigPrimitives = {
    scheduledStartAt: baseTimestamp + 300000,
    estimatedDurationMs: 3600000,
    defaultRoomConfig: {
      minSeats: 2,
      maxSeats: 8,
      avoidSingleton: true,
      disconnectionPolicy: { holdSeatForMs: 30000 },
    },
    maxParticipants: 50,
    defaultRoundDurationMs: 900000,
    autopilotPolicy: {
      allocationStategy: AllocationStrategy.ROUND_ROBIN,
    },
    lobbyAdmissionPolicy: {
      allowEarlyJoinMs: 300000,
      requireHostPresent: true
    },
    disconnectionPolicy: {
      holdSeatForMs: 120000,
    },
    lateJoinPolicy: {
      allocationMode: LateJoinAllocationMode.BEST_FIT
    },
    mode: SessionMode.HOSTED,
  };

  const validMainRoom: RoomPrimitives = {
    roomId: validMainRoomId,
    config: {
      minSeats: 2,
      maxSeats: 50, // Should match maxParticipants
      avoidSingleton: true,
      disconnectionPolicy: { holdSeatForMs: 120000 },
    },
    seats: Array.from({ length: 50 }, (_, i) => ({ seatNo: i })),
    createdAt: baseTimestamp,
  };

  const validSessionDto: SessionPrimitives = {
    sessionId: validSessionId,
    config: validSessionConfig,
    state: SessionState.SCHEDULED,
    createdByUserId: validAdminId,
    hostId: undefined,
    mainRoomId: validMainRoomId,
    currentRoundNo: 0,
    rounds: [],
    participants: [],
    rooms: [validMainRoom],
    createdAt: baseTimestamp,
  };

  describe('fromPrimitives happy path', () => {
    it('preserves all basic properties in roundtrip', () => {
      const session = Session.fromPrimitives(validSessionDto);
      const result = session.toPrimitives();

      expect(result.sessionId).toBe(validSessionDto.sessionId);
      expect(result.config).toEqual(validSessionDto.config);
      expect(result.state).toBe(validSessionDto.state);
      expect(result.createdByUserId).toBe(validSessionDto.createdByUserId);
      expect(result.hostId).toBe(validSessionDto.hostId);
      expect(result.mainRoomId).toBe(validSessionDto.mainRoomId);
      expect(result.currentRoundNo).toBe(validSessionDto.currentRoundNo);
      expect(result.createdAt).toBe(validSessionDto.createdAt);
    });

    it('preserves rooms in roundtrip', () => {
      const session = Session.fromPrimitives(validSessionDto);
      const result = session.toPrimitives();

      expect(result.rooms).toHaveLength(1);
      expect(result.rooms[0].roomId).toBe(validMainRoomId);
      expect(result.rooms[0].seats).toHaveLength(50);
    });

    it('handles session with participants', () => {
      const participants: ParticipantPresencePrimitives[] = [
        {
          participantId: validParticipantId1,
          tags: { role: ParticipantRole.MEMBER },
          joinedAt: baseTimestamp + 1000,
          currentRoomId: validMainRoomId,
        },
        {
          participantId: validParticipantId2,
          tags: { role: ParticipantRole.HOST },
          joinedAt: baseTimestamp + 2000,
        },
      ];

      const sessionDto: SessionPrimitives = {
        ...validSessionDto,
        participants,
        hostId: validParticipantId2,
      };

      const session = Session.fromPrimitives(sessionDto);
      const result = session.toPrimitives();

      expect(result.participants).toHaveLength(2);
      expect(result.participants[0].participantId).toBe(validParticipantId1);
      expect(result.participants[1].participantId).toBe(validParticipantId2);
      expect(result.hostId).toBe(validParticipantId2);
    });

    it('handles session with rounds', () => {
      const rounds: RoundPrimitives[] = [
        {
          roundNo: 1,
          kind: RoundKind.MAIN_TOPIC,
          durationMs: 900000,
          questions: ['What is your favorite color?'],
        },
        {
          roundNo: 2,
          kind: RoundKind.MAIN_TOPIC,
          durationMs: 900000,
          questions: ['Tell us about your hobby'],
          startedAt: baseTimestamp + 1000,
        },
      ];

      const sessionDto: SessionPrimitives = {
        ...validSessionDto,
        rounds,
        currentRoundNo: 1,
      };

      const session = Session.fromPrimitives(sessionDto);
      const result = session.toPrimitives();

      expect(result.rounds).toHaveLength(2);
      expect(result.rounds[0].roundNo).toBe(1);
      expect(result.rounds[1].roundNo).toBe(2);
      expect(result.currentRoundNo).toBe(1);
    });

    it('handles all session states correctly', () => {
      const states = [
        SessionState.SCHEDULED,
        SessionState.RUNNING,
        SessionState.PAUSED,
        SessionState.COMPLETED,
        SessionState.CANCELED,
      ];

      states.forEach(state => {
        const sessionDto: SessionPrimitives = {
          ...validSessionDto,
          state,
        };

        const session = Session.fromPrimitives(sessionDto);
        const result = session.toPrimitives();

        expect(result.state).toBe(state);
      });
    });
  });

  describe('fromPrimitives error cases', () => {
    it('throws PersistenceMappingError for null DTO', () => {
      expect(() => Session.fromPrimitives(null as any)).toThrow(PersistenceMappingError);
    });

    it('throws PersistenceMappingError for undefined DTO', () => {
      expect(() => Session.fromPrimitives(undefined as any)).toThrow(PersistenceMappingError);
    });

    it('throws PersistenceMappingError for invalid session state', () => {
      const invalidDto: SessionPrimitives = {
        ...validSessionDto,
        state: 'INVALID_STATE' as SessionState,
      };

      expect(() => Session.fromPrimitives(invalidDto)).toThrow(PersistenceMappingError);
    });

    it('throws PersistenceMappingError for invalid sessionId', () => {
      const invalidDto: SessionPrimitives = {
        ...validSessionDto,
        sessionId: 'invalid-uuid',
      };

      expect(() => Session.fromPrimitives(invalidDto)).toThrow(PersistenceMappingError);
    });

    it('throws PersistenceMappingError for invalid createdByUserId', () => {
      const invalidDto: SessionPrimitives = {
        ...validSessionDto,
        createdByUserId: 'invalid-uuid',
      };

      expect(() => Session.fromPrimitives(invalidDto)).toThrow(PersistenceMappingError);
    });

    it('throws PersistenceMappingError for invalid mainRoomId', () => {
      const invalidDto: SessionPrimitives = {
        ...validSessionDto,
        mainRoomId: 'invalid-uuid',
      };

      expect(() => Session.fromPrimitives(invalidDto)).toThrow(PersistenceMappingError);
    });

    it('throws PersistenceMappingError for invalid currentRoundNo', () => {
      const invalidDto: SessionPrimitives = {
        ...validSessionDto,
        currentRoundNo: -1,
      };

      expect(() => Session.fromPrimitives(invalidDto)).toThrow(PersistenceMappingError);
    });
  });
});
