import { Session } from '../session.aggregate';
import { SessionId } from '../../../value-objects/sessions/session-id/session-id.vo';
import { SessionConfig } from '../../../value-objects/sessions/session-config/session-config.vo';
import { SessionConfigPrimitives } from '../../../value-objects/sessions/contracts/session-config.type';
import { SessionState } from '../../../value-objects/sessions/contracts/session.state.enum';
import { SessionMode } from '../../../value-objects/sessions/contracts/session-mode.enum';
import { RoomId } from '../../../value-objects/rooms/room-id/room-id.vo';
import { Uuid } from '../../../primitives/uuid/uuid.primitive';
import { SessionStateError } from '../session.errors';
import { LateJoinAllocationMode } from '../../../policies/late-join/types/late-join-allocation-mode.enum';
import { AllocationStrategy } from '../../../policies/autopilot/types/allocation-strategy.enum';

describe('Session: State Management', () => {
  const validSessionId = '550e8400-e29b-41d4-a716-************';
  const validAdminId = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
  const validMainRoomId = '6ba7b810-9dad-41d1-80b4-00c04fd430c8';
  const baseTimestamp = 1640995200000; // 2022-01-01 00:00:00 UTC

  const validSessionConfig: SessionConfigPrimitives = {
    scheduledStartAt: baseTimestamp + 300000,
    estimatedDurationMs: 3600000,
    defaultRoomConfig: {
      minSeats: 2,
      maxSeats: 8,
      avoidSingleton: true,
      disconnectionPolicy: { holdSeatForMs: 30000 },
    },
    maxParticipants: 50,
    defaultRoundDurationMs: 900000,
    autopilotPolicy: {
      allocationStategy: AllocationStrategy.ROUND_ROBIN,
    },
    lobbyAdmissionPolicy: {
      allowEarlyJoinMs: 300000,
      requireHostPresent: true
    },
    disconnectionPolicy: {
      holdSeatForMs: 120000,
    },
    lateJoinPolicy: {
      allocationMode: LateJoinAllocationMode.BEST_FIT
    },
    mode: SessionMode.HOSTED,
  };

  function createTestSession(): Session {
    const sessionId = SessionId.fromPrimitives(validSessionId);
    const config = SessionConfig.fromPrimitives(validSessionConfig);
    const adminId = Uuid.fromPrimitives(validAdminId);
    const mainRoomId = RoomId.fromPrimitives(validMainRoomId);

    return Session.create(sessionId, config, adminId, mainRoomId);
  }

  describe('startSession', () => {
    it('transitions from SCHEDULED to RUNNING', () => {
      const session = createTestSession();
      expect(session.state).toBe(SessionState.SCHEDULED);

      const startedSession = session.startSession();
      expect(startedSession.state).toBe(SessionState.RUNNING);
      expect(startedSession.isRunning).toBe(true);
      expect(startedSession.isScheduled).toBe(false);
    });

    it('is idempotent when already RUNNING', () => {
      const session = createTestSession();
      const runningSession = session.startSession();
      const stillRunningSession = runningSession.startSession();

      expect(stillRunningSession.state).toBe(SessionState.RUNNING);
      expect(stillRunningSession).toBe(runningSession); // Should return same instance
    });

    it('is idempotent when PAUSED', () => {
      const session = createTestSession();
      const runningSession = session.startSession();
      const pausedSession = runningSession.pauseSession();
      const stillPausedSession = pausedSession.startSession();

      expect(stillPausedSession.state).toBe(SessionState.PAUSED);
      expect(stillPausedSession).toBe(pausedSession); // Should return same instance
    });

    it('throws SessionStateError when not in SCHEDULED state', () => {
      const session = createTestSession();
      const runningSession = session.startSession();
      const completedSession = runningSession.completeSession();

      expect(() => completedSession.startSession()).toThrow(SessionStateError);
    });
  });

  describe('pauseSession', () => {
    it('transitions from RUNNING to PAUSED', () => {
      const session = createTestSession();
      const runningSession = session.startSession();
      
      const pausedSession = runningSession.pauseSession();
      expect(pausedSession.state).toBe(SessionState.PAUSED);
      expect(pausedSession.isPaused).toBe(true);
      expect(pausedSession.isRunning).toBe(false);
    });

    it('is idempotent when already PAUSED', () => {
      const session = createTestSession();
      const runningSession = session.startSession();
      const pausedSession = runningSession.pauseSession();
      const stillPausedSession = pausedSession.pauseSession();

      expect(stillPausedSession.state).toBe(SessionState.PAUSED);
      expect(stillPausedSession).toBe(pausedSession); // Should return same instance
    });

    it('throws SessionStateError when not in RUNNING state', () => {
      const session = createTestSession();

      expect(() => session.pauseSession()).toThrow(SessionStateError);
    });
  });

  describe('resumeSession', () => {
    it('transitions from PAUSED to RUNNING', () => {
      const session = createTestSession();
      const runningSession = session.startSession();
      const pausedSession = runningSession.pauseSession();
      
      const resumedSession = pausedSession.resumeSession();
      expect(resumedSession.state).toBe(SessionState.RUNNING);
      expect(resumedSession.isRunning).toBe(true);
      expect(resumedSession.isPaused).toBe(false);
    });

    it('is idempotent when already RUNNING', () => {
      const session = createTestSession();
      const runningSession = session.startSession();
      const stillRunningSession = runningSession.resumeSession();

      expect(stillRunningSession.state).toBe(SessionState.RUNNING);
      expect(stillRunningSession).toBe(runningSession); // Should return same instance
    });

    it('throws SessionStateError when not in PAUSED state', () => {
      const session = createTestSession();

      expect(() => session.resumeSession()).toThrow(SessionStateError);
    });
  });

  describe('completeSession', () => {
    it('transitions from RUNNING to COMPLETED', () => {
      const session = createTestSession();
      const runningSession = session.startSession();
      
      const completedSession = runningSession.completeSession();
      expect(completedSession.state).toBe(SessionState.COMPLETED);
      expect(completedSession.isCompleted).toBe(true);
      expect(completedSession.isRunning).toBe(false);
    });

    it('transitions from PAUSED to COMPLETED', () => {
      const session = createTestSession();
      const runningSession = session.startSession();
      const pausedSession = runningSession.pauseSession();
      
      const completedSession = pausedSession.completeSession();
      expect(completedSession.state).toBe(SessionState.COMPLETED);
      expect(completedSession.isCompleted).toBe(true);
      expect(completedSession.isPaused).toBe(false);
    });

    it('is idempotent when already COMPLETED', () => {
      const session = createTestSession();
      const runningSession = session.startSession();
      const completedSession = runningSession.completeSession();
      const stillCompletedSession = completedSession.completeSession();

      expect(stillCompletedSession.state).toBe(SessionState.COMPLETED);
      expect(stillCompletedSession).toBe(completedSession); // Should return same instance
    });

    it('is idempotent when CANCELED', () => {
      const session = createTestSession();
      const canceledSession = session.cancelSession();
      const stillCanceledSession = canceledSession.completeSession();

      expect(stillCanceledSession.state).toBe(SessionState.CANCELED);
      expect(stillCanceledSession).toBe(canceledSession); // Should return same instance
    });

    it('throws SessionStateError when in SCHEDULED state', () => {
      const session = createTestSession();

      expect(() => session.completeSession()).toThrow(SessionStateError);
    });
  });

  describe('cancelSession', () => {
    it('transitions from SCHEDULED to CANCELED', () => {
      const session = createTestSession();
      
      const canceledSession = session.cancelSession();
      expect(canceledSession.state).toBe(SessionState.CANCELED);
      expect(canceledSession.isCanceled).toBe(true);
      expect(canceledSession.isScheduled).toBe(false);
    });

    it('transitions from RUNNING to CANCELED', () => {
      const session = createTestSession();
      const runningSession = session.startSession();
      
      const canceledSession = runningSession.cancelSession();
      expect(canceledSession.state).toBe(SessionState.CANCELED);
      expect(canceledSession.isCanceled).toBe(true);
      expect(canceledSession.isRunning).toBe(false);
    });

    it('transitions from PAUSED to CANCELED', () => {
      const session = createTestSession();
      const runningSession = session.startSession();
      const pausedSession = runningSession.pauseSession();
      
      const canceledSession = pausedSession.cancelSession();
      expect(canceledSession.state).toBe(SessionState.CANCELED);
      expect(canceledSession.isCanceled).toBe(true);
      expect(canceledSession.isPaused).toBe(false);
    });

    it('throws SessionStateError when already COMPLETED', () => {
      const session = createTestSession();
      const runningSession = session.startSession();
      const completedSession = runningSession.completeSession();

      expect(() => completedSession.cancelSession()).toThrow(SessionStateError);
    });

    it('throws SessionStateError when already CANCELED', () => {
      const session = createTestSession();
      const canceledSession = session.cancelSession();

      expect(() => canceledSession.cancelSession()).toThrow(SessionStateError);
    });
  });

  describe('State transition chains', () => {
    it('supports full lifecycle: SCHEDULED -> RUNNING -> PAUSED -> RUNNING -> COMPLETED', () => {
      const session = createTestSession();
      expect(session.state).toBe(SessionState.SCHEDULED);

      const runningSession = session.startSession();
      expect(runningSession.state).toBe(SessionState.RUNNING);

      const pausedSession = runningSession.pauseSession();
      expect(pausedSession.state).toBe(SessionState.PAUSED);

      const resumedSession = pausedSession.resumeSession();
      expect(resumedSession.state).toBe(SessionState.RUNNING);

      const completedSession = resumedSession.completeSession();
      expect(completedSession.state).toBe(SessionState.COMPLETED);
    });

    it('supports cancellation from any valid state', () => {
      const session = createTestSession();

      // From SCHEDULED
      const canceledFromScheduled = session.cancelSession();
      expect(canceledFromScheduled.state).toBe(SessionState.CANCELED);

      // From RUNNING
      const runningSession = session.startSession();
      const canceledFromRunning = runningSession.cancelSession();
      expect(canceledFromRunning.state).toBe(SessionState.CANCELED);

      // From PAUSED
      const pausedSession = runningSession.pauseSession();
      const canceledFromPaused = pausedSession.cancelSession();
      expect(canceledFromPaused.state).toBe(SessionState.CANCELED);
    });
  });
});
