import { ensure } from '../../support/ensure';
import { Uuid } from '../../primitives/uuid/uuid.primitive';
import { Instant } from '../../primitives/instant/instant.primitive';
import { SessionId } from '../../value-objects/sessions/session-id/session-id.vo';
import { SessionConfig } from '../../value-objects/sessions/session-config/session-config.vo';
import { ParticipantId } from '../../value-objects/participants/participant-id/participant-id.vo';
import { ParticipantRole } from '../../value-objects/participants/contracts/participant-role.enum';
import { RoomId } from '../../value-objects/rooms/room-id/room-id.vo';
import { Room } from '../room/room.aggregate';
import { Round } from '../../value-objects/sessions/round/round.entity';
import { ParticipantPresence } from '../../entities/participant-presence/presence.entity';
import { PersistenceMappingError } from '../../errors/persistence-mapping-error';
import {
  SessionStateError,
  SessionInvariantError,
  SessionCapacityError,
  SessionParticipantNotFoundError,
} from './session.errors';
import { ParticipantPresencePrimitives } from '../../entities/participant-presence/contracts/presence.type';
import { RoomConfig } from '../../value-objects/rooms/room-config/room-config.vo';
import { SessionPrimitives } from '../../value-objects/sessions/contracts/session.type';
import { SessionState } from '../../value-objects/sessions/contracts/session.state.enum';
import { PositiveInt } from '../../primitives/positive-int/positive-int.primitive';
import { NonNegativeInt } from '../../primitives/non-negative-int/non-negative-int.primitive';
import { RoomCapacityError } from '../room';
import { SessionVersion } from '../../value-objects/sessions/session-version.vo';
import { DomainEvent, EventPayload, createEvent } from '../../contracts/events/events';

export class Session {
  private constructor(
    public readonly _sessionId: SessionId,
    public readonly _config: SessionConfig,
    private _state: SessionState,
    public readonly _createdByUserId: Uuid,
    private _hostId: ParticipantId | undefined,
    public readonly _mainRoomId: RoomId,
    private _rounds: Round[],
    private _participants: ParticipantPresence[],
    private _rooms: Room[],
    private _currentRoundNo: NonNegativeInt,
    public readonly _createdAt: Instant,
    private _version: SessionVersion,
    private _uncommittedEvents: DomainEvent[] = [],
  ) {}

  static fromPrimitives(dto: SessionPrimitives): Session {
    try {
      ensure(
        dto != null,
        new PersistenceMappingError('Session DTO is null or undefined'),
      );

      const sessionId = SessionId.fromPrimitives(dto.sessionId);
      const config = SessionConfig.fromPrimitives(dto.config);
      const createdByUserId = Uuid.fromPrimitives(dto.createdByUserId);
      const hostId = dto.hostId
        ? ParticipantId.fromPrimitives(dto.hostId)
        : undefined;
      const mainRoomId = RoomId.fromPrimitives(dto.mainRoomId);
      const currentRoundNo = NonNegativeInt.fromPrimitives(dto.currentRoundNo);
      const createdAt = Instant.fromPrimitives(dto.createdAt);

      ensure(
        Object.values(SessionState).includes(dto.state),
        new PersistenceMappingError('Invalid session state', {
          state: dto.state,
        }),
      );

      const rounds = dto.rounds.map((roundDto) =>
        Round.fromPrimitives(roundDto),
      );
      const participants = dto.participants.map((participantDto) =>
        ParticipantPresence.fromPrimitives(participantDto),
      );
      const rooms = dto.rooms.map((roomDto) => Room.fromPrimitives(roomDto));

      return new Session(
        sessionId,
        config,
        dto.state,
        createdByUserId,
        hostId,
        mainRoomId,
        rounds,
        participants,
        rooms,
        currentRoundNo,
        createdAt,
        SessionVersion.fromPrimitives(dto.version || 0),
        [], // No uncommitted events when loading from persistence
      );
    } catch (error) {
      if (error instanceof PersistenceMappingError) {
        throw error;
      }
      throw new PersistenceMappingError(
        'Failed to map session from primitives',
        { originalError: error },
      );
    }
  }

  static create(
    sessionId: SessionId,
    config: SessionConfig,
    adminId: Uuid,
    mainRoomId: RoomId,
  ): Session {
    // Create main room

    // Create a special room config for the main room
    const mainRoomConfig = RoomConfig.fromPrimitives({
      minSeats: config.defaultRoomConfig.minSeats,
      maxSeats: config.maxParticipants, // maximum participants in a session (can be changed as needed)
      avoidSingleton: config.defaultRoomConfig.avoidSingleton,
      disconnectionPolicy: config.disconnectionPolicy,
    });

    const mainRoom = Room.create(mainRoomId, mainRoomConfig);

    // Events to emit if the update is successful
    // 1. SessionCreated (sessionId, config, mainRoomId, createdAt)
    // 2. MainRoomCreated (roomId, createdAt) -> mainRoom

    const session = new Session(
      sessionId,
      config,
      SessionState.SCHEDULED,
      adminId,
      undefined, // No host initially
      mainRoomId,
      [], // No rounds initially
      [], // No participants initially
      [mainRoom], // Start with main room
      NonNegativeInt.fromPrimitives(0),
      Instant.now(),
      SessionVersion.initial(),
      [],
    );

    // Emit SessionCreated event
    session.createSessionEvent('SessionCreated', {
      sessionId: sessionId.toPrimitives(),
      createdByUserId: adminId.toPrimitives(),
    });

    return session;
  }

  toPrimitives(): SessionPrimitives {
    return {
      sessionId: this._sessionId.toPrimitives(),
      config: this._config.toPrimitives(),
      state: this._state,
      createdByUserId: this._createdByUserId.toPrimitives(),
      hostId: this._hostId?.toPrimitives(),
      mainRoomId: this._mainRoomId.toPrimitives(),
      currentRoundNo: this._currentRoundNo.toPrimitives(),
      rounds: this._rounds.map((round) => round.toPrimitives()),
      participants: this._participants.map((participant) =>
        participant.toPrimitives(),
      ),
      rooms: this._rooms.map((room) => room.toPrimitives()),
      createdAt: this._createdAt.toPrimitives(),
      version: this._version.toPrimitives(),
    };
  }

  // ============================================================================
  // Version Management
  // ============================================================================

  getVersion(): number {
    return this._version.toPrimitives();
  }

  validateExpectedVersion(expectedVersion: number): void {
    this._version.validateExpected(expectedVersion);
  }

  private incrementVersion(): void {
    this._version = this._version.increment();
  }

  // ============================================================================
  // Event Management
  // ============================================================================

  getUncommittedEvents(): readonly DomainEvent[] {
    return [...this._uncommittedEvents];
  }

  markEventsAsCommitted(): void {
    this._uncommittedEvents = [];
  }

  private addEvent(event: DomainEvent): void {
    this._uncommittedEvents.push(event);
  }

  private createSessionEvent<K extends DomainEvent['kind']>(
    kind: K,
    payload: EventPayload<K>,
  ): void {
    this.incrementVersion();
    const event = createEvent(
      kind,
      payload,
      {
        correlationId: 'correlationId', // TODO: Get from command
        causationId: 'causationId', // TODO: Get from command
        occurredAt: Instant.now().toPrimitives(),
      },
    );
    this.addEvent(event);
  }

  startSession(): Session {
    if (
      this._state === SessionState.RUNNING ||
      this._state === SessionState.PAUSED
    )
      return this;

    ensure(
      this._state === SessionState.SCHEDULED,
      new SessionStateError('Cannot start session unless in SCHEDULED state', {
        currentState: this._state,
        sessionId: this._sessionId.toPrimitives(),
      }),
    );

    // Events to emit if the update is successful
    // 1. SessionStarted (sessionId, at)

    const newSession = new Session(
      this._sessionId,
      this._config,
      SessionState.RUNNING,
      this._createdByUserId,
      this._hostId,
      this._mainRoomId,
      this._rounds,
      this._participants,
      this._rooms,
      this._currentRoundNo,
      this._createdAt,
      this._version,
      [...this._uncommittedEvents],
    );

    newSession.createSessionEvent('SessionStarted', {
      sessionId: this._sessionId.toPrimitives(),
    });

    return newSession;
  }

  pauseSession(): Session {
    if (this._state === SessionState.PAUSED) return this;

    ensure(
      this._state === SessionState.RUNNING,
      new SessionStateError('Cannot pause session unless in RUNNING state', {
        currentState: this._state,
        sessionId: this._sessionId.toPrimitives(),
      }),
    );

    // Events to emit if the update is successful
    // 1. SessionPaused (sessionId, at)

    const newSession = new Session(
      this._sessionId,
      this._config,
      SessionState.PAUSED,
      this._createdByUserId,
      this._hostId,
      this._mainRoomId,
      this._rounds,
      this._participants,
      this._rooms,
      this._currentRoundNo,
      this._createdAt,
      this._version,
      [...this._uncommittedEvents],
    );

    newSession.createSessionEvent('SessionPaused', {
      sessionId: this._sessionId.toPrimitives(),
    });

    return newSession;
  }

  resumeSession(): Session {
    if (this._state === SessionState.RUNNING) return this;

    ensure(
      this._state === SessionState.PAUSED,
      new SessionStateError('Cannot resume session unless in PAUSED state', {
        currentState: this._state,
        sessionId: this._sessionId.toPrimitives(),
      }),
    );

    // Events to emit if the update is successful
    // 1. SessionResumed (sessionId, at)

    const newSession = new Session(
      this._sessionId,
      this._config,
      SessionState.RUNNING,
      this._createdByUserId,
      this._hostId,
      this._mainRoomId,
      this._rounds,
      this._participants,
      this._rooms,
      this._currentRoundNo,
      this._createdAt,
      this._version,
      [...this._uncommittedEvents],
    );

    newSession.createSessionEvent('SessionResumed', {
      sessionId: this._sessionId.toPrimitives(),
    });

    return newSession;
  }

  completeSession(): Session {
    if (
      this._state === SessionState.COMPLETED ||
      this._state === SessionState.CANCELED
    )
      return this;
    ensure(
      this._state === SessionState.RUNNING ||
        this._state === SessionState.PAUSED,
      new SessionStateError(
        'Cannot complete session unless in RUNNING or PAUSED state',
        {
          currentState: this._state,
          sessionId: this._sessionId.toPrimitives(),
        },
      ),
    );

    // Events to emit if the update is successful
    // 1. SessionCompleted (sessionId, at)

    const newSession = new Session(
      this._sessionId,
      this._config,
      SessionState.COMPLETED,
      this._createdByUserId,
      this._hostId,
      this._mainRoomId,
      this._rounds,
      this._participants,
      this._rooms,
      this._currentRoundNo,
      this._createdAt,
      this._version,
      [...this._uncommittedEvents],
    );

    newSession.createSessionEvent('SessionCompleted', {
      sessionId: this._sessionId.toPrimitives(),
    });

    return newSession;
  }

  cancelSession(): Session {
    ensure(
      this._state !== SessionState.COMPLETED &&
        this._state !== SessionState.CANCELED,
      new SessionStateError('Cannot cancel a completed session', {
        currentState: this._state,
        sessionId: this._sessionId.toPrimitives(),
      }),
    );

    // Events to emit if the update is successful
    // 1. SessionCanceled (sessionId, at)

    const newSession = new Session(
      this._sessionId,
      this._config,
      SessionState.CANCELED,
      this._createdByUserId,
      this._hostId,
      this._mainRoomId,
      this._rounds,
      this._participants,
      this._rooms,
      this._currentRoundNo,
      this._createdAt,
      this._version,
      [...this._uncommittedEvents],
    );

    newSession.createSessionEvent('SessionCanceled', {
      sessionId: this._sessionId.toPrimitives(),
    });

    return newSession;
  }

  // Getters for state inspection
  get state(): SessionState {
    return this._state;
  }

  get isScheduled(): boolean {
    return this._state === SessionState.SCHEDULED;
  }

  get isRunning(): boolean {
    return this._state === SessionState.RUNNING;
  }

  get isPaused(): boolean {
    return this._state === SessionState.PAUSED;
  }

  get isCompleted(): boolean {
    return this._state === SessionState.COMPLETED;
  }

  get isCanceled(): boolean {
    return this._state === SessionState.CANCELED;
  }

  get currentRound(): Round | undefined {
    return this._rounds.find(
      (r) => r.roundNo.toPrimitives() === this._currentRoundNo.toPrimitives(),
    );
  }

  get rounds(): Round[] {
    return [...this._rounds];
  }

  get participants(): ParticipantPresence[] {
    return [...this._participants];
  }

  get rooms(): Room[] {
    return [...this._rooms];
  }

  get hasHost(): boolean {
    return this._hostId != null;
  }

  // Round management methods
  addRound(round: Round): Session {
    const roundNo = round.roundNo.toPrimitives();
    const sessionIdStr = this._sessionId.toPrimitives();

    if (this._rounds.some((r) => r.roundNo.toPrimitives() === roundNo))
      return this;

    ensure(
      this._state !== SessionState.COMPLETED &&
        this._state !== SessionState.CANCELED,
      new SessionStateError('Cannot add round unless session is SCHEDULED', {
        currentState: this._state,
        sessionId: sessionIdStr,
        roundNo: roundNo.toString(),
      }),
    );

    const updatedRounds = [...this._rounds, round].sort(
      (a, b) => a.roundNo.toPrimitives() - b.roundNo.toPrimitives(),
    );

    ensure(
      /**
       * Round No are consecutive
       */
      updatedRounds.every((r, idx) => {
        const roundNo = r.roundNo.toPrimitives();

        const nextRound = updatedRounds[idx + 1];
        const prevRound = updatedRounds[idx - 1];

        return (
          (!nextRound || nextRound.roundNo.toPrimitives() === roundNo + 1) &&
          (!prevRound || prevRound.roundNo.toPrimitives() === roundNo - 1)
        );
      }),
      new SessionInvariantError('Round numbers must be consecutive', {
        sessionId: sessionIdStr,
      }),
    );

    // Events to emit if the update is successful
    // 1. RoundAddedToSession (sessionId, roundNo, at)

    const newSession = new Session(
      this._sessionId,
      this._config,
      this._state,
      this._createdByUserId,
      this._hostId,
      this._mainRoomId,
      updatedRounds,
      this._participants,
      this._rooms,
      this._currentRoundNo,
      this._createdAt,
      this._version,
      [...this._uncommittedEvents],
    );

    newSession.createSessionEvent('RoundAddedToSession', {
      sessionId: sessionIdStr,
      roundNo: roundNo,
      round: {
        roundNo: roundNo,
        kind: round.kind,
        durationMs: round.durationMs,
        questions: round.questions,
      },
    });

    return newSession;
  }

  startRound(roundNo: PositiveInt): Session {
    const roundNoStr = roundNo.toPrimitives();
    const sessionIdStr = this._sessionId.toPrimitives();
    const currentRoundNoStr = this._currentRoundNo.toPrimitives();

    ensure(
      this._state === SessionState.RUNNING,
      new SessionStateError('Cannot start round unless session is RUNNING', {
        currentState: this._state,
        sessionId: sessionIdStr,
        roundNo: roundNoStr,
      }),
    );

    ensure(
      roundNo.toPrimitives() <= this._rounds.length,
      new SessionInvariantError('Round No out of bounds', {
        currentRoundNo: currentRoundNoStr,
        totalRounds: this._rounds.length,
        sessionId: sessionIdStr,
      }),
    );

    const currentRound = NonNegativeInt.fromPrimitives(roundNo.toPrimitives());

    ensure(
      currentRound.toPrimitives() > this._currentRoundNo.toPrimitives(),
      new SessionInvariantError('Cannot start round that is already active', {
        currentRoundNo: currentRoundNoStr,
        roundNo: roundNoStr,
        sessionId: sessionIdStr,
      }),
    );

    ensure(
      !this._currentRoundNo.toPrimitives(),
      new SessionInvariantError(
        'Cannot start round while another is active, close current round first',
        {
          currentRoundNo: currentRoundNoStr,
          roundNo: roundNoStr,
          sessionId: sessionIdStr,
        },
      ),
    );

    // Events to emit if the update is successful
    // 1. RoundStarted (sessionId, roundNo, at)

    const newSession = new Session(
      this._sessionId,
      this._config,
      this._state,
      this._createdByUserId,
      this._hostId,
      this._mainRoomId,
      this._rounds,
      this._participants,
      this._rooms,
      currentRound,
      this._createdAt,
      this._version,
      [...this._uncommittedEvents],
    );

    newSession.createSessionEvent('RoundStarted', {
      sessionId: this._sessionId.toPrimitives(),
      roundNo: roundNo.toPrimitives(),
    });

    return newSession;
  }

  endCurrentRound(): Session {
    const currentRoundNoStr = this._currentRoundNo.toPrimitives();
    const sessionIdStr = this._sessionId.toPrimitives();

    ensure(
      this._state === SessionState.RUNNING,
      new SessionStateError('Cannot end round unless session is RUNNING', {
        currentState: this._state,
        sessionId: sessionIdStr,
        currentRoundNo: currentRoundNoStr,
      }),
    );
    if (this._currentRoundNo.toPrimitives() === 0) return this;

    ensure(
      this._currentRoundNo.toPrimitives() > 0 &&
        this._currentRoundNo.toPrimitives() <= this._rounds.length,
      new SessionInvariantError('No active round to end', {
        currentRoundNo: currentRoundNoStr,
        totalRounds: this._rounds.length,
        sessionId: sessionIdStr,
      }),
    );

    // Events to emit if the update is successful
    // 1. RoundEnded (sessionId, roundNo, at)

    const newSession = new Session(
      this._sessionId,
      this._config,
      this._state,
      this._createdByUserId,
      this._hostId,
      this._mainRoomId,
      this._rounds,
      this._participants,
      this._rooms,
      NonNegativeInt.fromPrimitives(0), // Reset current round to 0 (no active round))
      this._createdAt,
      this._version,
      [...this._uncommittedEvents],
    );

    newSession.createSessionEvent('RoundEnded', {
      sessionId: this._sessionId.toPrimitives(),
      roundNo: this._currentRoundNo.toPrimitives(),
    });

    return newSession;
  }

  addParticipant(
    participantId: ParticipantId,
    at: Instant,
    participantRole: ParticipantRole = ParticipantRole.MEMBER,
  ): Session {
    // Check if participant already exists (rejoin after disconnect)
    const existingParticipant = this._participants.find(
      (p) => p.participantId === participantId.toPrimitives(),
    );

    if (existingParticipant) {
      // No-op
      return this;
    }

    const updatedParticipants = [
      ...this._participants,
      ParticipantPresence.create(participantId, participantRole).onJoin(at),
    ];

    // Events to emit if the update is successful
    // 1. ParticipantAddedToSession (participantId, at)

    const newSession = new Session(
      this._sessionId,
      this._config,
      this._state,
      this._createdByUserId,
      this._hostId,
      this._mainRoomId,
      this._rounds,
      updatedParticipants,
      this._rooms,
      this._currentRoundNo,
      this._createdAt,
      this._version,
      [...this._uncommittedEvents],
    );

    newSession.createSessionEvent('ParticipantAddedToSession', {
      participantId: participantId.toPrimitives(),
      participantRole: participantRole,
      sessionId: this._sessionId.toPrimitives(),
    });

    return newSession;
  }

  // Participant management methods
  onJoin(
    participantId: ParticipantId,
    at: Instant,
    participantRole: ParticipantRole = ParticipantRole.MEMBER,
  ): Session {
    // 1. Verify session is running

    ensure(
      this._state === SessionState.RUNNING,
      new SessionStateError(
        'Cannot join participant unless session is RUNNING',
        {
          currentState: this._state,
          sessionId: this._sessionId.toPrimitives(),
          participantId: participantId.toPrimitives(),
        },
      ),
    );

    const participantIdStr = participantId.toPrimitives();
    const sessionIdStr = this._sessionId.toPrimitives();
    const currentRoundNo = this._currentRoundNo.toPrimitives();

    // 2. Verify session capacity hasn't been exceeded

    ensure(
      this.participants.length < this._config.maxParticipants,
      new SessionCapacityError('Session capacity exceeded', {
        currentCount: this.participants.length,
        maxParticipants: this._config.maxParticipants,
        sessionId: sessionIdStr,
        participantId: participantIdStr,
      }),
    );

    // Check if participant already exists (rejoin after disconnect)
    const existingParticipant = this._participants.find(
      (p) => p.participantId === participantIdStr,
    );

    if (existingParticipant) {
      // Probably a reconnect, try to restore the participant to their seat

      try {
        if (!currentRoundNo) {
          return this.assignParticipantToRoom(
            participantId,
            this._mainRoomId,
            at,
          );
        }
        return this.restoreReservedSeat(participantId, at);

        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (_) {
        // If restore fails, treat it as a new join

        // Add the participant to the participant presence first, then seat them
        return this.addParticipant(
          participantId,
          at,
          participantRole,
        ).seatParticipant(participantId, at);
      }
    }

    // Add the participant to the participant presence first, then seat them
    return this.addParticipant(
      participantId,
      at,
      participantRole,
    ).seatParticipant(participantId, at);
  }

  seatParticipant(participantId: ParticipantId, at: Instant): Session {
    /**
     * Seat the new participant in a room
     *
     * Cases:
     *
     * 1. No round is active
     *    Seat the participant in the main room
     *
     * 2. A round is active
     *    1. Find an existing room with space
     *    2. Create a new room if none exists
     *    3. Assign the participant to the room
     */

    ensure(
      this._state === SessionState.RUNNING,
      new SessionStateError(
        'Cannot seat participant unless session is RUNNING',
        {
          currentState: this._state,
          sessionId: this._sessionId.toPrimitives(),
          participantId: participantId.toPrimitives(),
        },
      ),
    );

    const participantIdStr = participantId.toPrimitives();
    const sessionIdStr = this._sessionId.toPrimitives();

    ensure(
      this._participants.some((p) => p.participantId === participantIdStr),
      new SessionParticipantNotFoundError('Participant not found', {
        participantId: participantIdStr,
        sessionId: sessionIdStr,
      }),
    );

    if (!this._currentRoundNo.toPrimitives()) {
      // No round is active, seat in main room
      return this.assignParticipantToRoom(participantId, this._mainRoomId, at);
    }

    // eslint-disable-next-line no-useless-catch
    try {
      // A round is active, find or create a room with space
      let targetRoom: Room | undefined = undefined;

      // Fill room progressively
      const candidateRooms = this._rooms
        .filter(
          (room) =>
            room.roomId.toPrimitives() !== this._mainRoomId.toPrimitives(),
        )
        .filter((room) => room.hasSpace)
        .sort(
          (a, b) =>
            a.occupiedSeats.length +
            a.reservedSeats.length -
            (b.occupiedSeats.length + b.reservedSeats.length),
        );

      if (candidateRooms.length > 0) {
        targetRoom = candidateRooms[0];
      } else {
        const newRoomId = RoomId.fromPrimitives(Uuid.generate());
        const sessionWithNewRoom = this.createBreakoutRoom(newRoomId, at);
        targetRoom = sessionWithNewRoom.findRoomById(newRoomId);

        ensure(
          targetRoom,
          new SessionInvariantError('New room creation failed', {
            sessionId: sessionIdStr,
            newRoomId: newRoomId.toPrimitives(),
          }),
        );

        return sessionWithNewRoom.assignParticipantToRoom(
          participantId,
          targetRoom.roomId,
          at,
        );
      }

      ensure(
        targetRoom,
        new SessionInvariantError('No target room found', {
          sessionId: sessionIdStr,
          participantId: participantIdStr,
        }),
      );

      // Assign participant to the target room
      return this.assignParticipantToRoom(participantId, targetRoom.roomId, at);
    } catch (error) {
      // Handle some edge cases : room full, etc.
      // Maybe consider the next room (in the nextroomtofills array)

      // for now, let us throw the error
      throw error;
    }
  }

  onSoftLeave(participantId: ParticipantId, at: Instant): Session {
    const participantIdStr = participantId.toPrimitives();

    let participantPresence = this._participants.find(
      (p) => p.participantId === participantIdStr,
    );

    if (!participantPresence) {
      // No-op if participant doesn't exist
      return this;
    }

    let currentParticipantRoom = this._rooms.find(
      (room) => room.roomId.toPrimitives() === participantPresence.roomId,
    );

    // Update participant's leave time (soft leave)
    participantPresence = participantPresence.onSoftLeave(at);

    const updatedParticipants = this._participants.map((p) =>
      p.participantId === participantIdStr ? participantPresence : p,
    );

    /**
     * THIS IS SOFT LEAVE : The participant can still reconnect within the grace period.
     *
     * We are not going to directly release the seat of the participant when they leave.
     *
     * Instead we will rely on the reconnection grace period to release the seat.
     *
     * When the participant tries to reconnect within the grace period, we will restore their room/seat.
     *
     * If the participant does not reconnect within the grace period, the seat (and room) will be released automatically.
     */

    // Reserve seat for reconnection if participant has a seat in a room

    if (currentParticipantRoom) {
      currentParticipantRoom = currentParticipantRoom.reserveSeatForReconnect(
        participantId,
        at,
      );
    }

    // Update rooms
    const updatedRooms = currentParticipantRoom
      ? this._rooms.map((room) =>
          room.roomId.toPrimitives() ===
          currentParticipantRoom.roomId.toPrimitives()
            ? currentParticipantRoom
            : // TODO: Start a timer to release the seat if the participant does not reconnect within the grace period
              room,
        )
      : this._rooms;

    // Events to emit if the update is successful
    // 1. ParticipantSoftLeft (participantId, at)

    const newSession = new Session(
      this._sessionId,
      this._config,
      this._state,
      this._createdByUserId,
      this._hostId,
      this._mainRoomId,
      this._rounds,
      updatedParticipants,
      updatedRooms,
      this._currentRoundNo,
      this._createdAt,
      this._version,
      [...this._uncommittedEvents],
    );

    newSession.createSessionEvent('ParticipantSoftLeft', {
      participantId: participantId.toPrimitives(),
      sessionId: this._sessionId.toPrimitives(),
    });

    return newSession;
  }

  assignParticipantToRoom(
    participantId: ParticipantId,
    targetRoomId: RoomId,
    at: Instant,
  ): Session {
    const participantIdStr = participantId.toPrimitives();
    const targetRoomIdStr = targetRoomId.toPrimitives();
    const sessionIdStr = this._sessionId.toPrimitives();

    ensure(
      this._state === SessionState.RUNNING,
      new SessionStateError(
        'Cannot assign participant to room unless session is RUNNING',
        {
          currentState: this._state,
          sessionId: sessionIdStr,
          participantId: participantIdStr,
          targetRoomId: targetRoomIdStr,
        },
      ),
    );

    // Participant presence
    let participantPresence = this._participants.find(
      (p) => p.participantId === participantIdStr,
    );

    ensure(
      participantPresence,
      new SessionParticipantNotFoundError('Participant not found', {
        participantId: participantIdStr,
        sessionId: sessionIdStr,
      }),
    );

    // Source room
    let sourceRoom = this._rooms.find((r) =>
      r.allSeats.some((seat) => seat.currentParticipantId === participantIdStr),
    );
    const sourceRoomIdStr = sourceRoom?.roomId.toPrimitives();

    // Target room
    let targetRoom = this._rooms.find(
      (r) => r.roomId.toPrimitives() === targetRoomIdStr,
    );

    ensure(
      targetRoom,
      new SessionInvariantError('Target room not found', {
        targetRoomId: targetRoomIdStr,
        sessionId: sessionIdStr,
      }),
    );

    if (sourceRoomIdStr === targetRoomIdStr) {
      return this; // or no-op/ensure
    }

    // Check if a seat is reserved for the participant in their current room
    const hasReservation = sourceRoom?.allSeats.some(
      (seat) =>
        seat.currentParticipantId === participantIdStr &&
        seat.isReservedForReconnect,
    );

    ensure(
      !hasReservation,
      new SessionInvariantError(
        'Cannot move participant to another room while their seat is reserved for reconnect',
        {
          participantId: participantIdStr,
          sessionId: sessionIdStr,
        },
      ),
    );

    ensure(
      targetRoom.hasSpace,
      new SessionCapacityError('Target room has no space', {
        targetRoomId: targetRoomIdStr,
        sessionId: sessionIdStr,
      }),
    );

    /**
     * Apply transition atomically (one method each on Room/Presence)
     */

    // 1) Release in source and exit (if seated)
    if (sourceRoom) {
      sourceRoom = sourceRoom.releaseSeatOfParticipant(participantId, at);

      // Update participant presence
      participantPresence = participantPresence.exitRoom(at);
    }

    // 2) Enter and Assign in target
    try {
      participantPresence = participantPresence.enterRoom(targetRoomId, at);
      targetRoom = targetRoom.assignParticipantToSeat(participantId, at);
    } catch (error) {
      if (error instanceof RoomCapacityError) {
        // TODO: Rollback

        throw new SessionCapacityError('Target room has no space', {
          targetRoomId: targetRoomIdStr,
          sessionId: sessionIdStr,
        });
      }
    }

    const updatedRooms = this._rooms.map((room) => {
      if (room.roomId.toPrimitives() === targetRoomIdStr) {
        return targetRoom;
      } else if (sourceRoom && room.roomId.toPrimitives() === sourceRoomIdStr) {
        return sourceRoom;
      } else {
        return room;
      }
    });

    const updatedParticipants = this._participants.map((p) => {
      if (p.participantId === participantIdStr) {
        return participantPresence;
      } else {
        return p;
      }
    });

    // Events to emit if the update is successful
    // If participant was in a room, emit 1. ParticipantMoved (fromRoomId, toRoomId, participantId, at, reason)
    // If participant was not in a room, emit 2. ParticipantSeated (roomId, participantId, at)

    // Update aggregate state

    const newSession = new Session(
      this._sessionId,
      this._config,
      this._state,
      this._createdByUserId,
      this._hostId,
      this._mainRoomId,
      this._rounds,
      updatedParticipants,
      updatedRooms,
      this._currentRoundNo,
      this._createdAt,
      this._version,
      [...this._uncommittedEvents],
    );

    if (sourceRoomIdStr) {
      newSession.createSessionEvent('ParticipantAssignedToRoom', {
        fromRoomId: sourceRoomIdStr,
        toRoomId: targetRoomIdStr,
        participantId: participantIdStr,
        sessionId: sessionIdStr,
      });
    } else {
      newSession.createSessionEvent('ParticipantSeated', {
        roomId: targetRoomIdStr,
        participantId: participantIdStr,
        sessionId: sessionIdStr,
      });
    }

    return newSession;
  }

  distributeParticipantsIntoBreakoutRooms(at: Instant): Session {
    // 1. Verify session is running
    ensure(
      this._state === SessionState.RUNNING,
      new SessionStateError(
        'Cannot redistribute participants unless session is RUNNING',
        {
          currentState: this._state,
          sessionId: this._sessionId.toPrimitives(),
        },
      ),
    );

    // 2. Verify there is an active round
    ensure(
      this._currentRoundNo.toPrimitives() > 0 &&
        this._currentRoundNo.toPrimitives() <= this._rounds.length,
      new SessionInvariantError('No active round to redistribute', {
        currentRoundNo: this._currentRoundNo.toPrimitives(),
        totalRounds: this._rounds.length,
        sessionId: this._sessionId.toPrimitives(),
      }),
    );

    // 3. Verify there are participants in the main room
    const mainRoom = this.findRoomById(this._mainRoomId);
    ensure(
      mainRoom && mainRoom.size > 0,
      new SessionInvariantError(
        'No participants in main room to redistribute',
        {
          mainRoomId: this._mainRoomId.toPrimitives(),
          sessionId: this._sessionId.toPrimitives(),
        },
      ),
    );

    // eslint-disable-next-line @typescript-eslint/no-this-alias
    let updatedSession: Session = this;

    // 4. Redistribute participants into breakout rooms

    for (const seat of mainRoom.occupiedSeats) {
      const participantId = ParticipantId.fromPrimitives(
        seat.currentParticipantId,
      );
      updatedSession = updatedSession.seatParticipant(participantId, at);
    }

    updatedSession.createSessionEvent('ParticipantsDistributed', {
      sessionId: this._sessionId.toPrimitives(),
      roundNo: this._currentRoundNo.toPrimitives(),
    });

    return updatedSession;
  }

  restoreReservedSeat(participantId: ParticipantId, at: Instant): Session {
    ensure(
      this._state === SessionState.RUNNING,
      new SessionStateError('Cannot restore seat unless session is RUNNING', {
        currentState: this._state,
        sessionId: this._sessionId.toPrimitives(),
        participantId: participantId.toPrimitives(),
      }),
    );

    const participantIdStr = participantId.toPrimitives();
    const sessionIdStr = this._sessionId.toPrimitives();

    let participantPresence = this._participants.find(
      (p) => p.participantId === participantIdStr,
    );

    ensure(
      participantPresence,
      new SessionParticipantNotFoundError('Participant not found', {
        participantId: participantIdStr,
        sessionId: sessionIdStr,
      }),
    );

    let reservedParticipantRoom = this._rooms.find((r) =>
      r.allSeats.some(
        (seat) =>
          seat.isReservedForReconnect &&
          seat.currentParticipantId === participantIdStr,
      ),
    );

    ensure(
      reservedParticipantRoom,
      new SessionInvariantError('Participant reserved room not found', {
        participantId: participantIdStr,
        sessionId: sessionIdStr,
      }),
    );

    participantPresence = participantPresence.enterRoom(
      reservedParticipantRoom.roomId,
      at,
    );

    reservedParticipantRoom = reservedParticipantRoom.restoreSeatAfterReconnect(
      participantId,
      at,
    );

    // Update rooms
    const updatedRooms = this._rooms.map((room) => {
      if (
        room.roomId.toPrimitives() ===
        reservedParticipantRoom.roomId.toPrimitives()
      ) {
        return reservedParticipantRoom;
      } else {
        return room;
      }
    });

    // Update participants
    const updatedParticipants = this._participants.map((p) =>
      p.participantId === participantIdStr ? participantPresence : p,
    );

    // Events to emit if the update is successful
    // 1. ParticipantRestored (roomId, participantId, at)

    const newSession = new Session(
      this._sessionId,
      this._config,
      this._state,
      this._createdByUserId,
      this._hostId,
      this._mainRoomId,
      this._rounds,
      updatedParticipants,
      updatedRooms,
      this._currentRoundNo,
      this._createdAt,
      this._version,
      [...this._uncommittedEvents],
    );

    newSession.createSessionEvent('ParticipantRestored', {
      participantId: participantIdStr,
      roomId: reservedParticipantRoom.roomId.toPrimitives(),
      sessionId: sessionIdStr,
    });

    return newSession;
  }

  setHost(hostId: ParticipantId): Session {
    if (this._hostId?.toPrimitives() === hostId.toPrimitives()) return this;

    const hostIdStr = hostId.toPrimitives();
    const sessionIdStr = this._sessionId.toPrimitives();

    const participant = this._participants.find(
      (p) => p.participantId === hostIdStr,
    );

    ensure(
      participant,
      new SessionInvariantError('Host must be a participant', {
        hostId: hostIdStr,
        sessionId: sessionIdStr,
      }),
    );

    ensure(
      participant.tags.role === ParticipantRole.HOST,
      new SessionInvariantError('Host must have HOST role', {
        hostId: hostIdStr,
        sessionId: sessionIdStr,
      }),
    );

    // Events to emit if the update is successful
    // 1. HostSet (hostId, at)

    const newSession = new Session(
      this._sessionId,
      this._config,
      this._state,
      this._createdByUserId,
      hostId,
      this._mainRoomId,
      this._rounds,
      this._participants,
      this._rooms,
      this._currentRoundNo,
      this._createdAt,
      this._version,
      [...this._uncommittedEvents],
    );

    newSession.createSessionEvent('HostSet', {
      hostId: hostIdStr,
      sessionId: sessionIdStr,
    });

    return newSession;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  createBreakoutRoom(roomId: RoomId, at: Instant): Session {
    const roomIdStr = roomId.toPrimitives();

    ensure(
      this._state === SessionState.RUNNING,
      new SessionStateError('Cannot create room unless session is RUNNING', {
        currentState: this._state,
        sessionId: this._sessionId.toPrimitives(),
        roomId: roomIdStr,
      }),
    );

    // Check if room already exists
    const existingRoom = this._rooms.find(
      (r) => r.roomId.toPrimitives() === roomIdStr,
    );

    if (existingRoom) {
      // No-op if room already exists
      return this;
    }

    const newRoom = Room.create(roomId, this._config.defaultRoomConfig);
    const updatedRooms = [...this._rooms, newRoom];

    // Events to emit if the update is successful
    // 1. BreakoutRoomCreated (roomId, at)

    const newSession = new Session(
      this._sessionId,
      this._config,
      this._state,
      this._createdByUserId,
      this._hostId,
      this._mainRoomId,
      this._rounds,
      this._participants,
      updatedRooms,
      this._currentRoundNo,
      this._createdAt,
      this._version,
      [...this._uncommittedEvents],
    );

    newSession.createSessionEvent('BreakoutRoomCreated', {
      roomId: roomIdStr,
      sessionId: this._sessionId.toPrimitives(),
    });

    return newSession;
  }

  // Utility methods for room and participant queries
  findRoomById(roomId: RoomId): Room | undefined {
    return this._rooms.find(
      (r) => r.roomId.toPrimitives() === roomId.toPrimitives(),
    );
  }

  findParticipantById(
    participantId: ParticipantId,
  ): ParticipantPresence | undefined {
    return this._participants.find(
      (p) => p.participantId === participantId.toPrimitives(),
    );
  }

  getParticipantRoom(participantId: ParticipantId): Room | undefined {
    return this._rooms.find((room) =>
      room.allSeats.some(
        (seat) => seat.currentParticipantId === participantId.toPrimitives(),
      ),
    );
  }

  getRoomsWithSpace(): Room[] {
    return this._rooms.filter((room) => room.hasSpace);
  }

  getFillingRooms(): Room[] {
    return this._rooms.filter((room) => room.isFilling);
  }

  getReadyRooms(): Room[] {
    return this._rooms.filter((room) => room.isReady);
  }

  returnAllParticipantsToMain(at: Instant): Session {
    ensure(
      this._state === SessionState.RUNNING,
      new SessionStateError(
        'Cannot return participants to main unless session is RUNNING',
        {
          currentState: this._state,
          sessionId: this._sessionId.toPrimitives(),
        },
      ),
    );

    const mainRoom = this.findRoomById(this._mainRoomId);
    const mainRoomIdStr = this._mainRoomId.toPrimitives();
    const sessionIdStr = this._sessionId.toPrimitives();

    ensure(
      mainRoom != null,
      new SessionInvariantError('Main room not found', {
        mainRoomId: mainRoomIdStr,
        sessionId: sessionIdStr,
      }),
    );

    // Collect all participants from breakout rooms
    const participantsToMove = this._rooms
      .filter(
        (room) =>
          room.roomId.toPrimitives() !== mainRoomIdStr &&
          room.occupiedSeats.length > 0,
      )
      .map((room) => ({
        participants: room.occupiedSeats.map((seat) =>
          ParticipantId.fromPrimitives(seat.currentParticipantId),
        ),
      }));

    // Move all participants to main room
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    let updatedSession: Session = this;

    for (const { participants } of participantsToMove) {
      for (const participantId of participants) {
        updatedSession = updatedSession.assignParticipantToRoom(
          participantId,
          this._mainRoomId,
          at,
        );
      }
    }

    // Events to emit if the update is successful
    // 1. ParticipantsReturnedToMain (sessionId, at)

    updatedSession.createSessionEvent('ParticipantsReturnedToMain', {
      sessionId: sessionIdStr,
    });

    return updatedSession;
  }

  /**
   * Rotate participants across existing breakout rooms using the diagonal-spread rule.
   *
   * Mapping rule:
   *   For each source room at index `rIdx` (r),
   *   enumerate its occupants as [p0, p1, p2, …].
   *   Each occupant at position `pIdx` moves to:
   *
   *       targetrIdx = (rIdx + pIdx) % R
   *
   * Where:
   *   - R            = number of breakout rooms (roomOrder.length)
   *   - roomOrder    = ordered list of breakout roomIds (stable each round)
   *   - rIdx         = index of the current room in roomOrder
   *   - fromRoomId   = actual roomId string from which the participant comes
   *   - occupants    = array of ParticipantPresencePrimitives currently in fromRoomId
   *   - pIdx         = occupant’s index in that occupants array (0..occupants.length-1)
   *   - participantId= unique identifier of the occupant (Uuid primitive string)
   *   - toRoomId     = roomOrder[(rIdx + pIdx) % R], the target breakout roomId
   *   - plan         = array of { participantId, fromRoomId, toRoomId } move instructions
   *   - now          = server time in ms epoch when moves are applied
   *   - maxGroupSize = largest occupants.length across all rooms (diagnostic only;
   *                    tells if the no-repeat invariant is guaranteed: maxGroupSize ≤ R)
   *
   * Invariant:
   *   - If maxGroupSize ≤ R, then no pair of participants who shared a room in round t
   *     can be together again in round t+1.
   *   - If maxGroupSize > R, repeats are mathematically unavoidable (pigeonhole principle).
   *
   *
   * Examples (numbers):
   *   1) R = 2 rooms; sizes 2 & 2  (M=2, R=2 → GUARANTEED)
   *      Room0: [A0, A1]
   *      Room1: [B0, B1]
   *      Mapping: (r+j) mod 2
   *        A0→0, A1→1;  B0→1, B1→0
   *      New: Room0=[A0,B1], Room1=[A1,B0]  → no pairs repeat.
   *
   *   2) R = 2 rooms; sizes 3 & 2  (M=3, R=2 → IMPOSSIBLE to guarantee)
   *      Room0: [A0, A1, A2] → A0→0, A1→1, A2→0 (collision A0 with A2)
   *      Room1: [B0, B1]     → B0→1, B1→0
   *      New: Room0=[A0,A2,B1], Room1=[A1,B0] → A0/A2 repeat (pigeonhole).
   *
   *   3) R = 3 rooms; each size 3  (M=3, R=3 → GUARANTEED)
   *      Room0: [A0,A1,A2] → 0,1,2
   *      Room1: [B0,B1,B2] → 1,2,0
   *      Room2: [C0,C1,C2] → 2,0,1
   *      Each former group spreads to all 3 rooms → no pairs repeat.
   *
   *   4) R = 4 rooms; each size 4  (M=4, R=4 → GUARANTEED)
   *      Room r occupants [X0..X3] map to rooms r, r+1, r+2, r+3 (all distinct).
   *
   * Optional UX tweak:
   *   If you want *everyone* to move (not even j=0 stays), add a rotating base offset:
   *     base = (this._currentRoundNo ?? 0) % R
   *     toIndex = (r + base + j) % R
   *   This keeps the guarantee the same; it only affects motion feel.
   */
  public rotateBreakouts(at: Instant): Session {
    if (this._currentRoundNo.toPrimitives() < 0) return this;
    // --- 0) Guards -------------------------------------------------------------
    ensure(
      this._state === SessionState.RUNNING,
      new SessionStateError('Cannot rotate rooms unless session is RUNNING', {
        currentState: this._state,
        sessionId: this._sessionId.toPrimitives(),
      }),
    );

    // Release all reserved seats before rotation
    let updatedSession = this.releaseAllReservedSeats(at);

    // --- 1) Build a stable room order (exclude Main) --------------------------
    const breakoutRooms = updatedSession._rooms.filter(
      (r) => r.roomId.toPrimitives() !== this._mainRoomId.toPrimitives(),
    );
    const roomOrder: string[] = breakoutRooms.map((r) =>
      r.roomId.toPrimitives(),
    );
    const R = roomOrder.length;
    if (R <= 1) return this; // per your policy: with 0/1 breakout rooms, do nothing.

    // --- 2) Snapshot: group current seated participants by roomId -------------
    // Deterministic per-room ordering → sort by (joinedAt, participantId)
    const occupantsByRoom = new Map<string, ParticipantPresencePrimitives[]>();
    for (const p of updatedSession._participants) {
      const rid = p.toPrimitives().currentRoomId;
      if (rid === this._mainRoomId.toPrimitives()) continue;
      if (!rid) continue; // not seated
      if (!roomOrder.includes(rid)) continue; // not a breakout room
      if (!occupantsByRoom.has(rid)) occupantsByRoom.set(rid, []);
      occupantsByRoom.get(rid)!.push(p.toPrimitives());
    }

    for (const list of occupantsByRoom.values()) {
      list.sort(
        (a, b) =>
          (a.joinedAt ?? 0) - (b.joinedAt ?? 0) ||
          a.participantId.localeCompare(b.participantId),
      );
    }

    // --- 3) Plan: diagonal spread over OCCUPANTS ONLY -------------------------
    type Move = { participantId: string; toRoomId: string };
    const plan: Move[] = [];

    for (let rIdx = 0; rIdx < R; rIdx++) {
      const fromRoomId = roomOrder[rIdx];
      const occupants = occupantsByRoom.get(fromRoomId) ?? [];

      for (let pIdx = 0; pIdx < occupants.length; pIdx++) {
        const pid = occupants[pIdx].participantId;
        const toRoomId = roomOrder[(rIdx + pIdx) % R];

        if (toRoomId !== fromRoomId) {
          plan.push({ participantId: pid, toRoomId });
        }
      }
    }

    // --- 4) Apply the move plan (room-level; seat choice is Room policy) ------

    // Phase A: release all movers' current seats

    const movers = new Set(plan.map((m) => m.participantId));
    for (const pid of movers) {
      updatedSession = updatedSession.releaseSeatByParticipant(
        ParticipantId.fromPrimitives(pid),
        at,
      );
    }

    // Phase B: move all movers to their new rooms
    for (const { participantId, toRoomId } of plan) {
      updatedSession = updatedSession.assignParticipantToRoom(
        ParticipantId.fromPrimitives(participantId),
        RoomId.fromPrimitives(toRoomId),
        at,
      );
    }

    // TODO: We need to re - evaluate if we do not have single occupancy rooms (according to policy)

    // Events to emit if the update is successful
    // 1. BreakoutRoomsRotated (roomId, at)

    updatedSession.createSessionEvent('BreakoutRoomsRotated', {
      sessionId: this._sessionId.toPrimitives(),
      moves: plan.map((m) => ({
        participantId: m.participantId,
        fromRoomId: roomOrder[(roomOrder.indexOf(m.toRoomId) + R - 1) % R],
        toRoomId: m.toRoomId,
      })),
    });

    return updatedSession;
  }

  releaseAllReservedSeats(at: Instant): Session {
    const reservedSeats = this.rooms.map((room) => room.reservedSeats).flat();

    const reservedParticipants = reservedSeats.map(
      (seat) => seat.currentParticipantId,
    );

    const updatedRooms = this._rooms.map((room) =>
      room.releaseAllReservedSeats(at),
    );

    const updatedPresence = this._participants.map((p) => {
      if (reservedParticipants.includes(p.participantId)) {
        return p.exitRoom(at);
      }
      return p;
    });

    // Events to emit if the update is successful
    // 1. AllReservedSeatsReleased (roomId, at)

    const newSession = new Session(
      this._sessionId,
      this._config,
      this._state,
      this._createdByUserId,
      this._hostId,
      this._mainRoomId,
      this._rounds,
      updatedPresence,
      updatedRooms,
      this._currentRoundNo,
      this._createdAt,
      this._version,
      [...this._uncommittedEvents],
    );

    newSession.createSessionEvent('AllReservedSeatsReleased', {
      sessionId: this._sessionId.toPrimitives(),
    });

    return newSession;
  }

  releaseSeatByParticipant(participantId: ParticipantId, at: Instant): Session {
    const participantIdStr = participantId.toPrimitives();

    const participantPresence = this._participants.find(
      (p) => p.participantId === participantIdStr,
    );

    ensure(
      participantPresence,
      new SessionParticipantNotFoundError('Participant not found', {
        participantId: participantIdStr,
        sessionId: this._sessionId.toPrimitives(),
      }),
    );

    const updatedRooms = this._rooms.map((room) => {
      if (room.roomId.toPrimitives() === participantPresence.roomId) {
        return room.releaseSeatOfParticipant(participantId, at);
      }
      return room;
    });

    // Events to emit if the update is successful
    // 1. SeatReleased (roomId, participantId, at)

    return new Session(
      this._sessionId,
      this._config,
      this._state,
      this._createdByUserId,
      this._hostId,
      this._mainRoomId,
      this._rounds,
      this._participants,
      updatedRooms,
      this._currentRoundNo,
      this._createdAt,
      this._version,
      [...this._uncommittedEvents],
    );
  }
}
