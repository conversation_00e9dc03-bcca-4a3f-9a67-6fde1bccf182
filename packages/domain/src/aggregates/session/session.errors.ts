import { DomainError, type ErrorContext } from '../../errors/domain-error';

export const ERR_AGGREGATE_SESSION_STATE = 'AGGREGATE.SESSION.STATE' as const;
export const ERR_AGGREGATE_SESSION_INVARIANT = 'AGGREGATE.SESSION.INVARIANT' as const;
export const ERR_AGGREGATE_SESSION_CAPACITY = 'AGGREGATE.SESSION.CAPACITY' as const;
export const ERR_AGGREGATE_SESSION_PARTICIPANT_NOT_FOUND = 'AGGREGATE.SESSION.PARTICIPANT_NOT_FOUND' as const;

export class SessionStateError extends DomainError {
  constructor(message: string, ctx?: ErrorContext) {
    super(ERR_AGGREGATE_SESSION_STATE, message, ctx);
  }
}

export class SessionInvariantError extends DomainError {
  constructor(message: string, ctx?: ErrorContext) {
    super(ERR_AGGREGATE_SESSION_INVARIANT, message, ctx);
  }
}

export class SessionCapacityError extends DomainError {
  constructor(message: string, ctx?: ErrorContext) {
    super(ERR_AGGREGATE_SESSION_CAPACITY, message, ctx);
  }
}

export class SessionParticipantNotFoundError extends DomainError {
  constructor(message: string, ctx?: ErrorContext) {
    super(ERR_AGGREGATE_SESSION_PARTICIPANT_NOT_FOUND, message, ctx);
  }
}
