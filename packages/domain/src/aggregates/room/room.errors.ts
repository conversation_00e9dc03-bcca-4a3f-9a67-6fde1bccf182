import { DomainError, type ErrorContext } from '../../errors/domain-error';

export const ERR_AGGREGATE_ROOM_CAPACITY = 'AGGREGATE.ROOM.CAPACITY' as const;
export const ERR_AGGREGATE_ROOM_STATE = 'AGGREGATE.ROOM.STATE' as const;
export const ERR_AGGREGATE_ROOM_PARTICIPANT_NOT_FOUND = 'AGGREGATE.ROOM.PARTICIPANT_NOT_FOUND' as const;

export class RoomCapacityError extends DomainError {
  constructor(message: string, ctx?: ErrorContext) {
    super(ERR_AGGREGATE_ROOM_CAPACITY, message, ctx);
  }
}

export class RoomStateError extends DomainError {
  constructor(message: string, ctx?: ErrorContext) {
    super(ERR_AGGREGATE_ROOM_STATE, message, ctx);
  }
}

export class RoomParticipantNotFoundError extends DomainError {
  constructor(message: string, ctx?: ErrorContext) {
    super(ERR_AGGREGATE_ROOM_PARTICIPANT_NOT_FOUND, message, ctx);
  }
}
