import { Room } from '../room.aggregate';
import { RoomParticipantNotFoundError } from '../room.errors';
import { RoomPrimitives } from '../../../value-objects/rooms/contracts/room.type';
import { RoomConfigPrimitives } from '../../../value-objects/rooms/contracts/room-config';
import { ParticipantId } from '../../../value-objects/participants/participant-id/participant-id.vo';
import { Instant } from '../../../primitives/instant/instant.primitive';

describe('Room: Seat Reconnection', () => {
  const validRoomId = '550e8400-e29b-41d4-a716-446655440000';
  const validParticipantId1 = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
  const baseTimestamp = 1640995200000; // 2022-01-01 00:00:00 UTC
  const laterTimestamp = 1640995260000; // 1 minute later

  const validRoomConfig: RoomConfigPrimitives = {
    minSeats: 2,
    maxSeats: 4,
    avoidSingleton: true,
    disconnectionPolicy: { holdSeatForMs: 30000 },
  };

  describe('restoreSeatAfterReconnect', () => {
    it('succeeds when seat is reserved for that participant', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [
          {
            seatNo: 0,
            participantId: validParticipantId1,
            reservedSince: baseTimestamp,
            assignedAt: baseTimestamp,
          },
          { seatNo: 1 },
          { seatNo: 2 },
          { seatNo: 3 },
        ],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);
      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(baseTimestamp + 10000); // Within deadline
      const seat = room.allSeats.find(
        (s) => s.currentParticipantId === validParticipantId1,
      );

      expect(seat.isReservedForReconnect).toBe(true);

      const updatedRoom = room.restoreSeatAfterReconnect(participantId, at);

      expect(updatedRoom.size).toBe(1);
      expect(updatedRoom.occupiedSeats).toHaveLength(1);
      expect(updatedRoom.reservedSeats).toHaveLength(0);
      expect(updatedRoom.availableSeats).toHaveLength(3);

      const restoredSeat = updatedRoom.allSeats.find(
        (s) => s.currentParticipantId === validParticipantId1,
      );
      expect(restoredSeat.seatNo.toPrimitives()).toBe(
        seat?.seatNo.toPrimitives(),
      );
      expect(restoredSeat.currentParticipantId).toBe(validParticipantId1);
      expect(restoredSeat.isOccupied).toBe(true);
      expect(restoredSeat.isReservedForReconnect).toBe(false);
    });

    it('throws RoomParticipantNotFoundError when no reservation exists', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [{ seatNo: 0 }, { seatNo: 1 }, { seatNo: 2 }, { seatNo: 3 }],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);
      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(laterTimestamp);

      expect(() => room.restoreSeatAfterReconnect(participantId, at)).toThrow(
        RoomParticipantNotFoundError,
      );

      try {
        room.restoreSeatAfterReconnect(participantId, at);
      } catch (error) {
        expect(error).toBeInstanceOf(RoomParticipantNotFoundError);
        expect((error as RoomParticipantNotFoundError).message).toBe(
          'Participant not found in reconnect state',
        );
        expect((error as RoomParticipantNotFoundError).ctx?.roomId).toBe(
          validRoomId,
        );
        expect((error as RoomParticipantNotFoundError).ctx?.participantId).toBe(
          validParticipantId1,
        );
      }
    });

    it('throws when participant is occupied but not reserved', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [
          {
            seatNo: 0,
            participantId: validParticipantId1,
            assignedAt: baseTimestamp,
          },
          { seatNo: 1 },
          { seatNo: 2 },
          { seatNo: 3 },
        ],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);
      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(laterTimestamp);

      try {
        room.restoreSeatAfterReconnect(participantId, at);
      } catch (error) {
        expect(error).toBeInstanceOf(RoomParticipantNotFoundError);
        expect((error as RoomParticipantNotFoundError).message).toBe(
          'Participant not found in reconnect state',
        );
        expect((error as RoomParticipantNotFoundError).ctx?.roomId).toBe(
          validRoomId,
        );
        expect((error as RoomParticipantNotFoundError).ctx?.participantId).toBe(
          validParticipantId1,
        );
      }
    });
  });
});
