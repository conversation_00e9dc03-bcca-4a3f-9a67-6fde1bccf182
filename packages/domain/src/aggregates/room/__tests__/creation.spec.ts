import { Room } from '../room.aggregate';
import { RoomId } from '../../../value-objects/rooms/room-id/room-id.vo';
import { RoomConfig } from '../../../value-objects/rooms/room-config/room-config.vo';
import { RoomConfigPrimitives } from '../../../value-objects/rooms/contracts/room-config';
import { SeatState } from '../../../entities/seat/contracts/seat-state.enum';
import { RoomState } from '../../../value-objects/rooms/contracts/room-state.enum';

describe('Room - Path 2: Creation', () => {
  const validRoomId = '550e8400-e29b-41d4-a716-446655440000';

  describe('Happy path', () => {
    it('creates exactly maxSeats seats, numbered 0..maxSeats-1, all EMPTY', () => {
      const roomConfig: RoomConfigPrimitives = {
        minSeats: 2,
        maxSeats: 4,
        avoidSingleton: true,
        disconnectionPolicy: { holdSeatForMs: 30000 },
      };

      const roomId = RoomId.fromPrimitives(validRoomId);
      const config = RoomConfig.fromPrimitives(roomConfig);

      const room = Room.create(roomId, config);

      // Verify seat creation
      expect(room.allSeats).toHaveLength(4); // maxSeats

      const seats = room.allSeats;
      expect(seats[0].seatNo.toPrimitives()).toBe(0);
      expect(seats[1].seatNo.toPrimitives()).toBe(1);
      expect(seats[2].seatNo.toPrimitives()).toBe(2);
      expect(seats[3].seatNo.toPrimitives()).toBe(3);

      // All seats should be empty
      seats.forEach(seat => {
        expect(seat.isEmpty).toBe(true);
        expect(seat.isOccupied).toBe(false);
        expect(seat.isReservedForReconnect).toBe(false);
        expect(seat.currentState).toBe(SeatState.EMPTY);
        expect(seat.currentParticipantId).toBeUndefined();
      });
    });

    it('creates room with different maxSeats configurations', () => {
      const testCases = [
        { minSeats: 2, maxSeats: 2 },
        { minSeats: 2, maxSeats: 6 },
        { minSeats: 3, maxSeats: 10 },
      ];

      testCases.forEach(({ minSeats, maxSeats }) => {
        const roomConfig: RoomConfigPrimitives = {
          minSeats,
          maxSeats,
          avoidSingleton: true,
          disconnectionPolicy: { holdSeatForMs: 30000 },
        };

        const roomId = RoomId.fromPrimitives(validRoomId);
        const config = RoomConfig.fromPrimitives(roomConfig);

        const room = Room.create(roomId, config);

        expect(room.allSeats).toHaveLength(maxSeats);
        
        // Verify seat numbering
        for (let i = 0; i < maxSeats; i++) {
          expect(room.allSeats[i].seatNo.toPrimitives()).toBe(i);
          expect(room.allSeats[i].isEmpty).toBe(true);
        }
      });
    });

    it('Should have initial state to FILLING', () => {
      const roomConfig: RoomConfigPrimitives = {
        minSeats: 2,
        maxSeats: 4,
        avoidSingleton: true,
        disconnectionPolicy: { holdSeatForMs: 30000 },
      };

      const roomId = RoomId.fromPrimitives(validRoomId);
      const config = RoomConfig.fromPrimitives(roomConfig);

      const room = Room.create(roomId, config);

      expect(room.currentState).toBe(RoomState.FILLING);
      expect(room.isFilling).toBe(true);
      expect(room.isReady).toBe(false);
      expect(room.isClosed).toBe(false);
    });

    it('Should have size to 0 initially', () => {
      const roomConfig: RoomConfigPrimitives = {
        minSeats: 2,
        maxSeats: 4,
        avoidSingleton: true,
        disconnectionPolicy: { holdSeatForMs: 30000 },
      };

      const roomId = RoomId.fromPrimitives(validRoomId);
      const config = RoomConfig.fromPrimitives(roomConfig);

      const room = Room.create(roomId, config);

      expect(room.size).toBe(0);
    });

    it('Should have availableSeats.length to maxSeats initially', () => {
      const roomConfig: RoomConfigPrimitives = {
        minSeats: 2,
        maxSeats: 4,
        avoidSingleton: true,
        disconnectionPolicy: { holdSeatForMs: 30000 },
      };

      const roomId = RoomId.fromPrimitives(validRoomId);
      const config = RoomConfig.fromPrimitives(roomConfig);

      const room = Room.create(roomId, config);

      expect(room.availableSeats).toHaveLength(4); // maxSeats
      expect(room.occupiedSeats).toHaveLength(0);
      expect(room.reservedSeats).toHaveLength(0);
    });

    it('creates immutable instance', () => {
      const roomConfig: RoomConfigPrimitives = {
        minSeats: 2,
        maxSeats: 4,
        avoidSingleton: true,
        disconnectionPolicy: { holdSeatForMs: 30000 },
      };

      const roomId = RoomId.fromPrimitives(validRoomId);
      const config = RoomConfig.fromPrimitives(roomConfig);

      const room1 = Room.create(roomId, config);
      const room2 = Room.create(roomId, config);

      // Different instances
      expect(room1).not.toBe(room2);
      
      // But equivalent content
      expect(room1.toPrimitives()).toEqual(room2.toPrimitives());
    });
  });
});
