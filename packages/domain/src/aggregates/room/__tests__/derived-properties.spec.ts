import { Room } from '../room.aggregate';
import { RoomPrimitives } from '../../../value-objects/rooms/contracts/room.type';
import { RoomConfigPrimitives } from '../../../value-objects/rooms/contracts/room-config';
import { RoomState } from '../../../value-objects/rooms/contracts/room-state.enum';

describe('Room: Derived Properties', () => {
  const validRoomId = '550e8400-e29b-41d4-a716-446655440000';
  const validParticipantId1 = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
  const validParticipantId2 = '6ba7b810-9dad-41d1-80b4-00c04fd430c8';
  const validParticipantId3 = '123e4567-e89b-42d3-a456-426614174000';
  const validParticipantId4 = '987fcdeb-51a2-43d1-9f12-345678901234';
  const baseTimestamp = 1640995200000; // 2022-01-01 00:00:00 UTC

  const validRoomConfig: RoomConfigPrimitives = {
    minSeats: 2,
    maxSeats: 4,
    avoidSingleton: true,
    disconnectionPolicy: { holdSeatForMs: 30000 },
  };

  describe('size calculation', () => {
    it('should correctly calculate size, occupied and reserved seats', () => {
      const testCases = [
        {
          description:
            'has space with 1 occupied, 1 reserved, 2 empty -> size = 2',
          seats: [
            {
              seatNo: 0,
              participantId: validParticipantId1,
              assignedAt: baseTimestamp,
            },
            {
              seatNo: 1,
              participantId: validParticipantId2,
              assignedAt: baseTimestamp,
              reservedSince: baseTimestamp,
            },
            { seatNo: 2 },
            { seatNo: 3 },
          ],
          expectedHasSpace: true,
          expectedSize: 2,
        },
        {
          description: 'no space with 2 occupied, 2 reserved -> size = 4',
          seats: [
            {
              seatNo: 0,
              participantId: validParticipantId1,
              assignedAt: baseTimestamp,
            },
            {
              seatNo: 1,
              participantId: validParticipantId2,
              assignedAt: baseTimestamp,
            },
            {
              seatNo: 2,
              participantId: validParticipantId3,
              reservedSince: baseTimestamp,
              assignedAt: baseTimestamp,
            },
            {
              seatNo: 3,
              participantId: validParticipantId4,
              reservedSince: baseTimestamp,
              assignedAt: baseTimestamp,
            },
          ],
          expectedHasSpace: false,
          expectedSize: 4,
        },
        {
          description: 'has space with 3 occupied, 1 empty -> size = 3',
          seats: [
            {
              seatNo: 0,
              participantId: validParticipantId1,
              assignedAt: baseTimestamp,
            },
            {
              seatNo: 1,
              participantId: validParticipantId2,
              assignedAt: baseTimestamp,
            },
            {
              seatNo: 2,
              participantId: validParticipantId3,
              assignedAt: baseTimestamp,
            },
            { seatNo: 3 },
          ],
          expectedHasSpace: true,
          expectedSize: 3,
        },
      ];

      testCases.forEach(({ seats, expectedHasSpace, expectedSize }) => {
        const roomDto: RoomPrimitives = {
          roomId: validRoomId,
          config: validRoomConfig,
          seats,
          createdAt: baseTimestamp,
        };

        const room = Room.fromPrimitives(roomDto);

        expect(room.hasSpace).toBe(expectedHasSpace);
        expect(room.size).toBe(expectedSize);
      });
    });
  });

  describe('seat partitioning', () => {
    it('partitions are disjoint and sum to maxSeats', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [
          {
            seatNo: 0,
            participantId: validParticipantId1,
            assignedAt: baseTimestamp,
          },
          {
            seatNo: 1,
            participantId: validParticipantId2,
            reservedSince: baseTimestamp,
            assignedAt: baseTimestamp,
          },
          { seatNo: 2 },
          { seatNo: 3 },
        ],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);

      const occupiedSeats = room.occupiedSeats;
      const reservedSeats = room.reservedSeats;
      const availableSeats = room.availableSeats;

      // Verify counts
      expect(occupiedSeats).toHaveLength(1);
      expect(reservedSeats).toHaveLength(1);
      expect(availableSeats).toHaveLength(2);

      // Verify sum equals maxSeats
      expect(
        occupiedSeats.length + reservedSeats.length + availableSeats.length,
      ).toBe(4);

      // Verify disjoint (no seat appears in multiple partitions)
      const allSeatNos = [
        ...occupiedSeats.map((s) => s.seatNo.toPrimitives()),
        ...reservedSeats.map((s) => s.seatNo.toPrimitives()),
        ...availableSeats.map((s) => s.seatNo.toPrimitives()),
      ];
      const uniqueSeatNos = new Set(allSeatNos);
      expect(uniqueSeatNos.size).toBe(allSeatNos.length);
    });
  });

  describe('RoomState', () => {
    it('should correctly determine room state based on seats states', () => {
      const testCases = [
        {
          description: 'FILLING when all seats are EMPTY',
          seats: [{ seatNo: 0 }, { seatNo: 1 }, { seatNo: 2 }, { seatNo: 3 }],
          expectedState: RoomState.FILLING,
        },
        {
          description: 'FILLING when size < minSeats',
          seats: [
            {
              seatNo: 0,
              participantId: validParticipantId1,
              assignedAt: baseTimestamp,
            },
            { seatNo: 1 },
            { seatNo: 2 },
            { seatNo: 3 },
          ],
          expectedState: RoomState.FILLING,
        },
        {
          description: 'READY when size == minSeats',
          seats: [
            {
              seatNo: 0,
              participantId: validParticipantId1,
              assignedAt: baseTimestamp,
            },
            {
              seatNo: 1,
              participantId: validParticipantId2,
              assignedAt: baseTimestamp,
            },
            { seatNo: 2 },
            { seatNo: 3 },
          ],
          expectedState: RoomState.READY,
        },
        {
          description: 'READY when size >= minSeats',
          seats: [
            {
              seatNo: 0,
              participantId: validParticipantId1,
              assignedAt: baseTimestamp,
            },
            {
              seatNo: 1,
              participantId: validParticipantId2,
              reservedSince: baseTimestamp,
              assignedAt: baseTimestamp,
            },
            {
              seatNo: 2,
              participantId: validParticipantId3,
              assignedAt: baseTimestamp,
            },
            { seatNo: 3 },
          ],
          expectedState: RoomState.READY,
        },
        {
          description: 'CLOSED when all seats are occupied',
          seats: [
            {
              seatNo: 0,
              participantId: validParticipantId1,
              assignedAt: baseTimestamp,
            },
            {
              seatNo: 1,
              participantId: validParticipantId2,
              assignedAt: baseTimestamp,
            },
            {
              seatNo: 2,
              participantId: validParticipantId3,
              assignedAt: baseTimestamp,
            },
            {
              seatNo: 3,
              participantId: validParticipantId4,
              assignedAt: baseTimestamp,
            },
          ],
          expectedState: RoomState.CLOSED,
        },
        {
          description: 'CLOSED when all seats are reserved',
          seats: [
            {
              seatNo: 0,
              participantId: validParticipantId1,
              reservedSince: baseTimestamp,
              assignedAt: baseTimestamp,
            },
            {
              seatNo: 1,
              participantId: validParticipantId2,
              reservedSince: baseTimestamp,
              assignedAt: baseTimestamp,
            },
            {
              seatNo: 2,
              participantId: validParticipantId3,
              reservedSince: baseTimestamp,
              assignedAt: baseTimestamp,
            },
            {
              seatNo: 3,
              participantId: validParticipantId4,
              reservedSince: baseTimestamp,
              assignedAt: baseTimestamp,
            },
          ],
          expectedState: RoomState.CLOSED,
        },
      ];

      testCases.forEach(({ seats, expectedState }) => {
        const roomDto: RoomPrimitives = {
          roomId: validRoomId,
          config: validRoomConfig,
          seats,
          createdAt: baseTimestamp,
        };

        const room = Room.fromPrimitives(roomDto);
        expect(room.currentState).toBe(expectedState);
      });
    });
  });
});
