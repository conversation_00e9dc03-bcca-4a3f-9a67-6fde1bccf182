import { Room } from '../room.aggregate';
import { RoomPrimitives } from '../../../value-objects/rooms/contracts/room.type';
import { RoomConfigPrimitives } from '../../../value-objects/rooms/contracts/room-config';
import { RoomState } from '../../../value-objects/rooms/contracts/room-state.enum';
import { ParticipantId } from '../../../value-objects/participants/participant-id/participant-id.vo';
import { Instant } from '../../../primitives/instant/instant.primitive';
import { RoomParticipantNotFoundError } from '../room.errors';

describe('Room: Seat Releasing', () => {
  const validRoomId = '550e8400-e29b-41d4-a716-446655440000';
  const validParticipantId1 = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
  const validParticipantId2 = '6ba7b810-9dad-41d1-80b4-00c04fd430c8';
  const validParticipantId3 = '123e4567-e89b-42d3-a456-426614174000';
  const validParticipantId4 = '987fcdeb-51a2-43d1-9f12-345678901234';
  const baseTimestamp = 1640995200000; // 2022-01-01 00:00:00 UTC
  const laterTimestamp = 1640995260000; // 1 minute later

  const validRoomConfig: RoomConfigPrimitives = {
    minSeats: 2,
    maxSeats: 4,
    avoidSingleton: true,
    disconnectionPolicy: { holdSeatForMs: 30000 },
  };

  describe('releaseSeatOfParticipant', () => {
    it('releases seat when participant is OCCUPIED', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [
          {
            seatNo: 0,
            participantId: validParticipantId1,
            assignedAt: baseTimestamp,
          },
          { seatNo: 1 },
          { seatNo: 2 },
          { seatNo: 3 },
        ],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);
      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(laterTimestamp);

      const updatedRoom = room.releaseSeatOfParticipant(participantId, at);

      expect(updatedRoom.size).toBe(0);
      expect(updatedRoom.occupiedSeats).toHaveLength(0);
      expect(updatedRoom.availableSeats).toHaveLength(4);

      // Verify the seat is now empty
      const releasedSeat = updatedRoom.allSeats.find(
        (s) => s.seatNo.toPrimitives() === 0,
      );
      expect(releasedSeat?.isEmpty).toBe(true);
      expect(releasedSeat?.currentParticipantId).toBeUndefined();
    });

    it('decreases size after release', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [
          {
            seatNo: 0,
            participantId: validParticipantId1,
            assignedAt: baseTimestamp,
          },
          {
            seatNo: 1,
            participantId: validParticipantId2,
            assignedAt: baseTimestamp,
          },
          { seatNo: 2 },
          { seatNo: 3 },
        ],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);
      expect(room.size).toBe(2);

      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(laterTimestamp);
      const updatedRoom = room.releaseSeatOfParticipant(participantId, at);

      expect(updatedRoom.size).toBe(1);
    });

    it('increases availableSeats after release', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [
          {
            seatNo: 0,
            participantId: validParticipantId1,
            assignedAt: baseTimestamp,
          },
          {
            seatNo: 1,
            participantId: validParticipantId2,
            assignedAt: baseTimestamp,
          },
          { seatNo: 2 },
          { seatNo: 3 },
        ],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);
      expect(room.availableSeats).toHaveLength(2);

      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(laterTimestamp);
      const updatedRoom = room.releaseSeatOfParticipant(participantId, at);

      expect(updatedRoom.availableSeats).toHaveLength(3);
    });

    it('throws error if participant not found', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [
          {
            seatNo: 0,
            participantId: validParticipantId1,
            assignedAt: baseTimestamp,
          },
          { seatNo: 1 },
          { seatNo: 2 },
          { seatNo: 3 },
        ],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);
      const participantId = ParticipantId.fromPrimitives(validParticipantId2); // Not in room
      const at = Instant.fromPrimitives(laterTimestamp);

      try {
        room.releaseSeatOfParticipant(participantId, at);
      } catch (error) {
        expect(error).toBeInstanceOf(RoomParticipantNotFoundError);
        expect((error as RoomParticipantNotFoundError).message).toBe(
          'Participant not found',
        );
        expect((error as RoomParticipantNotFoundError).ctx?.roomId).toBe(
          validRoomId,
        );
        expect((error as RoomParticipantNotFoundError).ctx?.participantId).toBe(
          validParticipantId2,
        );
      }
    });

    it('Releases the seat even if is reserved for reconnect', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [
          {
            seatNo: 0,
            participantId: validParticipantId1,
            reservedSince: baseTimestamp,
            assignedAt: baseTimestamp,
          },
          { seatNo: 1 },
          { seatNo: 2 },
          { seatNo: 3 },
        ],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);
      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(laterTimestamp);

      const result = room.releaseSeatOfParticipant(participantId, at);

      expect(result.size).toBe(0);
      expect(result.reservedSeats).toHaveLength(0);
      expect(result.availableSeats).toHaveLength(4);
    });

    it('Should transition from CLOSED to READY when seat is released', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [
          {
            seatNo: 0,
            participantId: validParticipantId1,
            assignedAt: baseTimestamp,
          },
          {
            seatNo: 1,
            participantId: validParticipantId2,
            assignedAt: baseTimestamp,
          },
          {
            seatNo: 2,
            participantId: validParticipantId3,
            assignedAt: baseTimestamp,
          },
          {
            seatNo: 3,
            participantId: validParticipantId4,
            assignedAt: baseTimestamp,
          },
        ],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);
      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(laterTimestamp);

      const updatedRoom = room.releaseSeatOfParticipant(participantId, at);

      expect(updatedRoom.currentState).toBe(RoomState.READY);
    });

    it('Should transition from READY to FILLING when seat is released', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [
          {
            seatNo: 0,
            participantId: validParticipantId1,
            assignedAt: baseTimestamp,
          },
          {
            seatNo: 1,
            participantId: validParticipantId2,
            assignedAt: baseTimestamp,
          },
          { seatNo: 2 },
          { seatNo: 3 },
        ],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);
      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(laterTimestamp);

      const updatedRoom = room.releaseSeatOfParticipant(participantId, at);

      expect(updatedRoom.currentState).toBe(RoomState.FILLING);
    });
  });

  describe('releaseAllReservedSeats', () => {
    it('converts all RESERVED_FOR_RECONNECT seats to EMPTY', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [
          {
            seatNo: 0,
            participantId: validParticipantId1,
            assignedAt: baseTimestamp,
          },
          {
            seatNo: 1,
            participantId: validParticipantId2,
            reservedSince: baseTimestamp,
            assignedAt: baseTimestamp - 1000,
          },
          {
            seatNo: 2,
            participantId: validParticipantId3,
            reservedSince: baseTimestamp,
            assignedAt: baseTimestamp - 2000,
          },
          { seatNo: 3 },
        ],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);
      const at = Instant.fromPrimitives(laterTimestamp);

      const updatedRoom = room.releaseAllReservedSeats(at);

      expect(updatedRoom.size).toBe(1); // Only occupied seat counts
      expect(updatedRoom.occupiedSeats).toHaveLength(1);
      expect(updatedRoom.reservedSeats).toHaveLength(0);
      expect(updatedRoom.availableSeats).toHaveLength(3);

      // Verify occupied seat unchanged
      const occupiedSeat = updatedRoom.occupiedSeats[0];
      expect(occupiedSeat.seatNo.toPrimitives()).toBe(0);
      expect(occupiedSeat.currentParticipantId).toBe(validParticipantId1);

      // Verify previously reserved seats are now empty
      const seat1 = updatedRoom.allSeats.find(
        (s) => s.seatNo.toPrimitives() === 1,
      );
      const seat2 = updatedRoom.allSeats.find(
        (s) => s.seatNo.toPrimitives() === 2,
      );
      expect(seat1?.isEmpty).toBe(true);
      expect(seat2?.isEmpty).toBe(true);
    });

    it('leaves other seat states unchanged', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [
          {
            seatNo: 0,
            participantId: validParticipantId1,
            assignedAt: baseTimestamp,
          },
          { seatNo: 1 },
          {
            seatNo: 2,
            participantId: validParticipantId2,
            reservedSince: baseTimestamp,
            assignedAt: baseTimestamp,
          },
          { seatNo: 3 },
        ],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);
      const at = Instant.fromPrimitives(laterTimestamp);

      const updatedRoom = room.releaseAllReservedSeats(at);

      // Occupied and empty seats should remain unchanged
      const seat0 = updatedRoom.allSeats.find(
        (s) => s.seatNo.toPrimitives() === 0,
      );
      const seat1 = updatedRoom.allSeats.find(
        (s) => s.seatNo.toPrimitives() === 1,
      );
      const seat3 = updatedRoom.allSeats.find(
        (s) => s.seatNo.toPrimitives() === 3,
      );

      expect(seat0?.isOccupied).toBe(true);
      expect(seat0?.currentParticipantId).toBe(validParticipantId1);
      expect(seat1?.isEmpty).toBe(true);
      expect(seat3?.isEmpty).toBe(true);

      // Only reserved seat should be released
      const seat2 = updatedRoom.allSeats.find(
        (s) => s.seatNo.toPrimitives() === 2,
      );
      expect(seat2?.isEmpty).toBe(true);
    });
  });
});
