/* eslint-disable @typescript-eslint/no-explicit-any */
import { Room } from '../room.aggregate';
import { PersistenceMappingError } from '../../../errors/persistence-mapping-error';
import { RoomPrimitives } from '../../../value-objects/rooms/contracts/room.type';
import { RoomConfigPrimitives } from '../../../value-objects/rooms/contracts/room-config';

describe('Room: Mapping & Roundtrip', () => {
  const validRoomId = '550e8400-e29b-41d4-a716-446655440000';
  const validParticipantId1 = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
  const validParticipantId2 = '6ba7b810-9dad-41d1-80b4-00c04fd430c8';
  const baseTimestamp = 1640995200000; // 2022-01-01 00:00:00 UTC

  const validRoomConfig: RoomConfigPrimitives = {
    minSeats: 2,
    maxSeats: 4,
    avoidSingleton: true,
    disconnectionPolicy: { holdSeatForMs: 30000 },
  };

  const validRoomDto: RoomPrimitives = {
    roomId: validRoomId,
    config: validRoomConfig,
    seats: [
      { seatNo: 0 },
      { seatNo: 1 },
      { seatNo: 2 },
      { seatNo: 3 },
    ],
    createdAt: baseTimestamp,
  };

  describe('fromPrimitives happy path', () => {
    it('preserves roomId, config, createdAt, and seat states/indices in roundtrip', () => {
      const roomDTO: RoomPrimitives = {
        ...validRoomDto,
        seats: [
          { seatNo: 0, participantId: validParticipantId1, assignedAt: baseTimestamp },
          { seatNo: 1, participantId: validParticipantId2, reservedSince: baseTimestamp + 1000 },
          { seatNo: 2 },
          { seatNo: 3 },
        ],
      };

      const room = Room.fromPrimitives(roomDTO);
      const result = room.toPrimitives();

      expect(result.roomId).toBe(roomDTO.roomId);
      expect(result.config).toEqual(roomDTO.config);
      expect(result.createdAt).toBe(roomDTO.createdAt);

      // Verify seat preservation
      expect(result.seats).toHaveLength(4);
    });
  });

  describe('error cases', () => {
    it('throws PersistenceMappingError on null DTO', () => {
      expect(() => Room.fromPrimitives(null as any)).toThrow(PersistenceMappingError);
      
      try {
        Room.fromPrimitives(null as any);
      } catch (error) {
        expect(error).toBeInstanceOf(PersistenceMappingError);
        expect((error as PersistenceMappingError).message).toBe('Room DTO is null or undefined');
      }
    });

    it('throws PersistenceMappingError on undefined DTO', () => {
      expect(() => Room.fromPrimitives(undefined as any)).toThrow(PersistenceMappingError);
      
      try {
        Room.fromPrimitives(undefined as any);
      } catch (error) {
        expect(error).toBeInstanceOf(PersistenceMappingError);
        expect((error as PersistenceMappingError).message).toBe('Room DTO is null or undefined');
      }
    });

    describe('seat length mismatch', () => {
      it('throws when seats.length !== config.maxSeats (too few)', () => {
        const invalidDto = {
          ...validRoomDto,
          seats: [
            { seatNo: 0 },
            { seatNo: 1 },
            // Missing 2 seats for maxSeats=4
          ],
        };
        
        expect(() => Room.fromPrimitives(invalidDto)).toThrow(PersistenceMappingError);
        
        try {
          Room.fromPrimitives(invalidDto);
        } catch (error) {
          expect(error).toBeInstanceOf(PersistenceMappingError);
          expect((error as PersistenceMappingError).message).toBe('Number of seats does not match max seats');
        }
      });

      it('throws when seats.length !== config.maxSeats (too many)', () => {
        const invalidDto = {
          ...validRoomDto,
          seats: [
            { seatNo: 0 },
            { seatNo: 1 },
            { seatNo: 2 },
            { seatNo: 3 },
            { seatNo: 4 }, // Extra seat
          ],
        };
        
        expect(() => Room.fromPrimitives(invalidDto)).toThrow(PersistenceMappingError);
      });
    });

    describe('seat number validation', () => {
      it('throws when seat numbers are out of range (negative)', () => {
        const invalidDto = {
          ...validRoomDto,
          seats: [
            { seatNo: -1 }, // Invalid
            { seatNo: 1 },
            { seatNo: 2 },
            { seatNo: 3 },
          ],
        };
        
        expect(() => Room.fromPrimitives(invalidDto)).toThrow(PersistenceMappingError);
        
        try {
          Room.fromPrimitives(invalidDto);
        } catch (error) {
          expect(error).toBeInstanceOf(PersistenceMappingError);
          expect((error as PersistenceMappingError).message).toBe('Seat number out of range');
          expect((error as PersistenceMappingError).ctx?.seatNos).toEqual([-1, 1, 2, 3]);
        }
      });

      it('throws when seat numbers are out of range (>= maxSeats)', () => {
        const invalidDto = {
          ...validRoomDto,
          seats: [
            { seatNo: 0 },
            { seatNo: 1 },
            { seatNo: 2 },
            { seatNo: 4 }, // Invalid (>= maxSeats=4)
          ],
        };
        
        expect(() => Room.fromPrimitives(invalidDto)).toThrow(PersistenceMappingError);
        
        try {
          Room.fromPrimitives(invalidDto);
        } catch (error) {
          expect(error).toBeInstanceOf(PersistenceMappingError);
          expect((error as PersistenceMappingError).message).toBe('Seat number out of range');
          expect((error as PersistenceMappingError).ctx?.seatNos).toEqual([0, 1, 2, 4]);
        }
      });

      it('throws when seat numbers are not integers', () => {
        const invalidDto = {
          ...validRoomDto,
          seats: [
            { seatNo: 0.5 }, // Invalid
            { seatNo: 1 },
            { seatNo: 2 },
            { seatNo: 3 },
          ],
        };
        
        expect(() => Room.fromPrimitives(invalidDto)).toThrow(PersistenceMappingError);
      });

      it('throws on duplicate seat numbers', () => {
        const invalidDto = {
          ...validRoomDto,
          seats: [
            { seatNo: 0 },
            { seatNo: 1 },
            { seatNo: 1 }, // Duplicate
            { seatNo: 3 },
          ],
        };
        
        expect(() => Room.fromPrimitives(invalidDto)).toThrow(PersistenceMappingError);
        
        try {
          Room.fromPrimitives(invalidDto);
        } catch (error) {
          expect(error).toBeInstanceOf(PersistenceMappingError);
          expect((error as PersistenceMappingError).message).toBe('Duplicate seat numbers');
          expect((error as PersistenceMappingError).ctx?.seatNos).toEqual([0, 1, 1, 3]);
        }
      });
    });
  });
});
