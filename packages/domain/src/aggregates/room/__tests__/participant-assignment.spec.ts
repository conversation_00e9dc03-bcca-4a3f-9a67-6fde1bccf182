import { Room } from '../room.aggregate';
import { RoomCapacityError, RoomStateError } from '../room.errors';
import { RoomPrimitives } from '../../../value-objects/rooms/contracts/room.type';
import { RoomConfigPrimitives } from '../../../value-objects/rooms/contracts/room-config';
import { RoomState } from '../../../value-objects/rooms/contracts/room-state.enum';
import { ParticipantId } from '../../../value-objects/participants/participant-id/participant-id.vo';
import { Instant } from '../../../primitives/instant/instant.primitive';

describe('Room: Assigning Participant', () => {
  const validRoomId = '550e8400-e29b-41d4-a716-446655440000';
  const validParticipantId1 = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
  const validParticipantId2 = '6ba7b810-9dad-41d1-80b4-00c04fd430c8';
  const validParticipantId3 = '123e4567-e89b-42d3-a456-426614174000';
  const validParticipantId4 = '987fcdeb-51a2-43d1-9f12-345678901234';
  const baseTimestamp = 1640995200000; // 2022-01-01 00:00:00 UTC

  const validRoomConfig: RoomConfigPrimitives = {
    minSeats: 2,
    maxSeats: 4,
    avoidSingleton: true,
    disconnectionPolicy: { holdSeatForMs: 30000 },
  };

  describe('happy path in FILLING state', () => {
    it('assigns participant to first empty seat', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [{ seatNo: 0 }, { seatNo: 1 }, { seatNo: 2 }, { seatNo: 3 }],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);
      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(baseTimestamp);

      const updatedRoom = room.assignParticipantToSeat(participantId, at);

      expect(updatedRoom.size).toBe(1);
      expect(updatedRoom.occupiedSeats).toHaveLength(1);
      expect(updatedRoom.availableSeats).toHaveLength(3);

      const occupiedSeat = updatedRoom.occupiedSeats[0];
      expect(occupiedSeat.seatNo.toPrimitives()).toBe(0); // First seat
      expect(occupiedSeat.currentParticipantId).toBe(validParticipantId1);
      expect(occupiedSeat.isOccupied).toBe(true);
    });

    it('assignment skips occupied and reserved seats deterministically', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [
          {
            seatNo: 0,
            participantId: validParticipantId1,
            reservedSince: baseTimestamp,
            assignedAt: baseTimestamp,
          },
          { seatNo: 1 },
          {
            seatNo: 2,
            participantId: validParticipantId2,
            assignedAt: baseTimestamp,
          },
          { seatNo: 3 },
        ],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);
      const newParticipantId =
        ParticipantId.fromPrimitives(validParticipantId3);
      const at = Instant.fromPrimitives(baseTimestamp);

      const updatedRoom = room.assignParticipantToSeat(newParticipantId, at);

      // Should assign to seat 1 (lowest available index, skipping reserved seat 0)
      const newlyOccupiedSeat = updatedRoom.occupiedSeats.find(
        (s) => s.currentParticipantId === validParticipantId3,
      );
      expect(newlyOccupiedSeat?.seatNo.toPrimitives()).toBe(1);
    });

    it('assigns to lowest-index empty seat when some seats are occupied', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [
          { seatNo: 1 },
          { seatNo: 2 },
          { seatNo: 3 },
          {
            seatNo: 0,
            participantId: validParticipantId1,
            assignedAt: baseTimestamp,
          },
        ],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);
      const participantId = ParticipantId.fromPrimitives(validParticipantId2);
      const at = Instant.fromPrimitives(baseTimestamp);

      const updatedRoom = room.assignParticipantToSeat(participantId, at);

      expect(updatedRoom.size).toBe(2);
      expect(updatedRoom.occupiedSeats).toHaveLength(2);

      const newlyOccupiedSeat = updatedRoom.occupiedSeats.find(
        (s) => s.currentParticipantId === validParticipantId2,
      );
      expect(newlyOccupiedSeat?.seatNo.toPrimitives()).toBe(1); // Lowest available index
    });

    it('increases size after assignment', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [
          {
            seatNo: 0,
            participantId: validParticipantId1,
            assignedAt: baseTimestamp,
          },
          { seatNo: 1 },
          { seatNo: 2 },
          { seatNo: 3 },
        ],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);
      expect(room.size).toBe(1);

      const participantId = ParticipantId.fromPrimitives(validParticipantId2);
      const at = Instant.fromPrimitives(baseTimestamp);
      const updatedRoom = room.assignParticipantToSeat(participantId, at);

      expect(updatedRoom.size).toBe(2);
    });

    it('Should transition to READY when min seats reached', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [
          {
            seatNo: 0,
            participantId: validParticipantId1,
            assignedAt: baseTimestamp,
          },
          { seatNo: 1 },
          { seatNo: 2 },
          { seatNo: 3 },
        ],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);
      const participantId = ParticipantId.fromPrimitives(validParticipantId2);
      const at = Instant.fromPrimitives(baseTimestamp);

      const updatedRoom = room.assignParticipantToSeat(participantId, at);

      expect(updatedRoom.currentState).toBe(RoomState.READY);
    });
  });

  describe('happy path in READY state', () => {
    it('  Should transition to CLOSED when max seats reached', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [
          {
            seatNo: 0,

            participantId: validParticipantId1,
            assignedAt: baseTimestamp,
          },
          {
            seatNo: 1,

            participantId: validParticipantId2,
            assignedAt: baseTimestamp,
          },
          {
            seatNo: 2,

            participantId: validParticipantId3,
            assignedAt: baseTimestamp,
          },
          { seatNo: 3 },
        ],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);
      const participantId = ParticipantId.fromPrimitives(validParticipantId4);
      const at = Instant.fromPrimitives(baseTimestamp);

      const updatedRoom = room.assignParticipantToSeat(participantId, at);

      expect(updatedRoom.size).toBe(4);
      expect(updatedRoom.occupiedSeats).toHaveLength(4);
      expect(updatedRoom.currentState).toBe(RoomState.CLOSED);
    });
  });

  describe('capacity errors', () => {
    it('fails with RoomCapacityError when fully occupied/reserved', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [
          {
            seatNo: 0,

            participantId: validParticipantId1,
            assignedAt: baseTimestamp,
          },
          {
            seatNo: 1,

            participantId: validParticipantId2,
            assignedAt: baseTimestamp,
          },
          {
            seatNo: 2,

            participantId: validParticipantId3,
            assignedAt: baseTimestamp,
          },
          {
            seatNo: 3,

            participantId: validParticipantId4,
            assignedAt: baseTimestamp,
          },
        ],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);
      const participantId = ParticipantId.fromPrimitives(validParticipantId4);
      const at = Instant.fromPrimitives(baseTimestamp);

      try {
        room.assignParticipantToSeat(participantId, at);
      } catch (error) {
        expect(error).toBeInstanceOf(RoomCapacityError);
        expect((error as RoomCapacityError).message).toBe(
          'Room capacity exceeded',
        );
        expect((error as RoomCapacityError).ctx?.currentSize).toBe(4);
        expect((error as RoomCapacityError).ctx?.maxSeats).toBe(4);
      }
    });

    it('fails with RoomCapacityError when fully reserved', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [
          {
            seatNo: 0,

            participantId: validParticipantId1,
            reservedSince: baseTimestamp,
            assignedAt: baseTimestamp,
          },
          {
            seatNo: 1,

            participantId: validParticipantId2,
            reservedSince: baseTimestamp,
            assignedAt: baseTimestamp,
          },
          {
            seatNo: 2,

            participantId: validParticipantId3,
            reservedSince: baseTimestamp,
            assignedAt: baseTimestamp,
          },
          {
            seatNo: 3,
            assignedAt: baseTimestamp,
            participantId: validParticipantId4,
            reservedSince: baseTimestamp,
          },
        ],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);
      const participantId = ParticipantId.fromPrimitives(validParticipantId4);
      const at = Instant.fromPrimitives(baseTimestamp);

      try {
        room.assignParticipantToSeat(participantId, at);
      } catch (error) {
        expect(error).toBeInstanceOf(RoomCapacityError);
        expect((error as RoomCapacityError).message).toBe(
          'Room capacity exceeded',
        );
        expect((error as RoomCapacityError).ctx?.currentSize).toBe(4);
        expect((error as RoomCapacityError).ctx?.maxSeats).toBe(4);
      }
    });
  });

  describe('duplicate participant errors', () => {
    it('fails with RoomStateError if participant is already occupied', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [
          {
            seatNo: 0,
            participantId: validParticipantId1,
            assignedAt: baseTimestamp,
          },
          { seatNo: 1 },
          { seatNo: 2 },
          { seatNo: 3 },
        ],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);
      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(baseTimestamp);

      try {
        room.assignParticipantToSeat(participantId, at);
      } catch (error) {
        expect(error).toBeInstanceOf(RoomStateError);
        expect((error as RoomStateError).message).toBe(
          'Participant already in room',
        );
        expect((error as RoomStateError).ctx?.roomId).toBe(validRoomId);
        expect((error as RoomStateError).ctx?.participantId).toBe(
          validParticipantId1,
        );
      }
    });

    it('fails with RoomStateError if participant is already reserved', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [
          {
            seatNo: 0,
            participantId: validParticipantId1,
            reservedSince: baseTimestamp,
            assignedAt: baseTimestamp,
          },
          { seatNo: 1 },
          { seatNo: 2 },
          { seatNo: 3 },
        ],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);
      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(baseTimestamp);

      try {
        room.assignParticipantToSeat(participantId, at);
      } catch (error) {
        expect(error).toBeInstanceOf(RoomStateError);
        expect((error as RoomStateError).message).toBe(
          'Participant already in room',
        );
        expect((error as RoomStateError).ctx?.roomId).toBe(validRoomId);
        expect((error as RoomStateError).ctx?.participantId).toBe(
          validParticipantId1,
        );
      }
    });
  });
  describe('deterministic seat assignment', () => {
    it('always assigns to lowest-index empty seat', () => {
      // Test multiple scenarios to ensure determinism
      const scenarios = [
        {
          description: 'empty seats at indices 1 and 3',
          seats: [
            {
              seatNo: 0,
              participantId: validParticipantId1,
              assignedAt: baseTimestamp,
            },
            { seatNo: 1 },
            {
              seatNo: 2,
              participantId: validParticipantId2,
              assignedAt: baseTimestamp,
            },
            { seatNo: 3 },
          ],
          expectedAssignmentIndex: 1,
        },
        {
          description: 'empty seats at indices 0, 2, and 3',
          seats: [
            { seatNo: 0 },
            {
              seatNo: 1,
              participantId: validParticipantId1,
              assignedAt: baseTimestamp,
            },
            { seatNo: 2 },
            { seatNo: 3 },
          ],
          expectedAssignmentIndex: 0,
        },
        {
          description: 'only seat 3 is empty',
          seats: [
            {
              seatNo: 0,
              participantId: validParticipantId1,
              assignedAt: baseTimestamp,
            },
            {
              seatNo: 1,
              participantId: validParticipantId2,
              reservedSince: baseTimestamp,
              assignedAt: baseTimestamp,
            },
            {
              seatNo: 2,
              participantId: validParticipantId3,
              assignedAt: baseTimestamp,
            },
            { seatNo: 3 },
          ],
          expectedAssignmentIndex: 3,
        },
      ];

      scenarios.forEach(({ seats, expectedAssignmentIndex }) => {
        const roomDto: RoomPrimitives = {
          roomId: validRoomId,
          config: validRoomConfig,
          seats,
          createdAt: baseTimestamp,
        };

        const room = Room.fromPrimitives(roomDto);
        const newParticipantId = ParticipantId.fromPrimitives(
          '987fcdeb-51a2-43d1-9f12-345678902231',
        );
        const at = Instant.fromPrimitives(baseTimestamp);

        const updatedRoom = room.assignParticipantToSeat(newParticipantId, at);

        const assignedSeat = updatedRoom.occupiedSeats.find(
          (s) =>
            s.currentParticipantId === '987fcdeb-51a2-43d1-9f12-345678902231',
        );
        expect(assignedSeat?.seatNo.toPrimitives()).toBe(
          expectedAssignmentIndex,
        );
      });
    });

    it('assignment order is consistent across multiple calls', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [{ seatNo: 0 }, { seatNo: 1 }, { seatNo: 2 }, { seatNo: 3 }],
        createdAt: baseTimestamp,
      };

      let room = Room.fromPrimitives(roomDto);
      const at = Instant.fromPrimitives(baseTimestamp);

      // Assign participants one by one
      const participant1 = ParticipantId.fromPrimitives(validParticipantId1);
      room = room.assignParticipantToSeat(participant1, at);

      const participant2 = ParticipantId.fromPrimitives(validParticipantId2);
      room = room.assignParticipantToSeat(participant2, at);

      const participant3 = ParticipantId.fromPrimitives(validParticipantId3);
      room = room.assignParticipantToSeat(participant3, at);

      const participant4 = ParticipantId.fromPrimitives(validParticipantId4);
      room = room.assignParticipantToSeat(participant4, at);

      // Verify assignment order
      const occupiedSeats = room.occupiedSeats.sort(
        (a, b) => a.seatNo.toPrimitives() - b.seatNo.toPrimitives(),
      );

      expect(occupiedSeats[0].currentParticipantId).toBe(validParticipantId1);
      expect(occupiedSeats[1].currentParticipantId).toBe(validParticipantId2);
      expect(occupiedSeats[2].currentParticipantId).toBe(validParticipantId3);
      expect(occupiedSeats[3].currentParticipantId).toBe(validParticipantId4);

      expect(occupiedSeats[0].seatNo.toPrimitives()).toBe(0);
      expect(occupiedSeats[1].seatNo.toPrimitives()).toBe(1);
      expect(occupiedSeats[2].seatNo.toPrimitives()).toBe(2);
      expect(occupiedSeats[3].seatNo.toPrimitives()).toBe(3);
    });
  });
});
