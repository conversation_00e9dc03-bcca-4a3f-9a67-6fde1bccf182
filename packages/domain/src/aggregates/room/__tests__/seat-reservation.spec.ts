import { Room } from '../room.aggregate';
import { RoomPrimitives } from '../../../value-objects/rooms/contracts/room.type';
import { RoomConfigPrimitives } from '../../../value-objects/rooms/contracts/room-config';
import { ParticipantId } from '../../../value-objects/participants/participant-id/participant-id.vo';
import { Instant } from '../../../primitives/instant/instant.primitive';
import { RoomParticipantNotFoundError } from '../room.errors';

describe('Room: Seat Reservation', () => {
  const validRoomId = '550e8400-e29b-41d4-a716-446655440000';
  const validParticipantId1 = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
  const validParticipantId2 = '6ba7b810-9dad-41d1-80b4-00c04fd430c8';
  const baseTimestamp = 1640995200000; // 2022-01-01 00:00:00 UTC
  const laterTimestamp = 1640995260000; // 1 minute later

  const validRoomConfig: RoomConfigPrimitives = {
    minSeats: 2,
    maxSeats: 4,
    avoidSingleton: true,
    disconnectionPolicy: { holdSeatForMs: 30000 },
  };

  describe('reserveSeatForReconnect', () => {
    it('converts OCCUPIED seat to RESERVED_FOR_RECONNECT', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [
          {
            seatNo: 0,
            participantId: validParticipantId1,
            assignedAt: baseTimestamp,
          },
          { seatNo: 1 },
          { seatNo: 2 },
          { seatNo: 3 },
        ],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);
      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(laterTimestamp);

      const updatedRoom = room.reserveSeatForReconnect(participantId, at);

      expect(updatedRoom.size).toBe(1); // Still counts toward size
      expect(updatedRoom.occupiedSeats).toHaveLength(0);
      expect(updatedRoom.reservedSeats).toHaveLength(1);
      expect(updatedRoom.availableSeats).toHaveLength(3);

      const reservedSeat = updatedRoom.reservedSeats[0];
      expect(reservedSeat.seatNo.toPrimitives()).toBe(0);
      expect(reservedSeat.currentParticipantId).toBe(validParticipantId1);
      expect(reservedSeat.isReservedForReconnect).toBe(true);
    });

    it('throws error if participant not found', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [
          {
            seatNo: 0,
            participantId: validParticipantId1,
            assignedAt: baseTimestamp,
          },
          { seatNo: 1 },
          { seatNo: 2 },
          { seatNo: 3 },
        ],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);
      const participantId = ParticipantId.fromPrimitives(validParticipantId2); // Not in room
      const at = Instant.fromPrimitives(laterTimestamp);

      try {
        room.reserveSeatForReconnect(participantId, at);
      } catch (error) {
        expect(error).toBeInstanceOf(RoomParticipantNotFoundError);
        expect((error as RoomParticipantNotFoundError).message).toBe(
          'Participant not found',
        );
        expect((error as RoomParticipantNotFoundError).ctx?.roomId).toBe(
          validRoomId,
        );
        expect((error as RoomParticipantNotFoundError).ctx?.participantId).toBe(
          validParticipantId2,
        );
      }
    });

    it('throws error if participant already reserved', () => {
      const roomDto: RoomPrimitives = {
        roomId: validRoomId,
        config: validRoomConfig,
        seats: [
          {
            seatNo: 0,
            participantId: validParticipantId1,
            reservedSince: baseTimestamp,
            assignedAt: baseTimestamp,
          },
          { seatNo: 1 },
          { seatNo: 2 },
          { seatNo: 3 },
        ],
        createdAt: baseTimestamp,
      };

      const room = Room.fromPrimitives(roomDto);
      const participantId = ParticipantId.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(laterTimestamp);

      try {
        room.reserveSeatForReconnect(participantId, at);
      } catch (error) {
        expect(error).toBeInstanceOf(RoomParticipantNotFoundError);
        expect((error as RoomParticipantNotFoundError).message).toBe(
          'Participant not found',
        );
        expect((error as RoomParticipantNotFoundError).ctx?.roomId).toBe(
          validRoomId,
        );
        expect((error as RoomParticipantNotFoundError).ctx?.participantId).toBe(
          validParticipantId1,
        );
      }
    })
  });
});
