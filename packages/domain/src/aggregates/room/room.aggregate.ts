import { ensure } from '../../support/ensure';
import { Instant } from '../../primitives/instant/instant.primitive';
import { RoomId } from '../../value-objects/rooms/room-id/room-id.vo';
import { RoomConfig } from '../../value-objects/rooms/room-config/room-config.vo';
import { Seat } from '../../entities/seat/seat.entity';
import { PersistenceMappingError } from '../../errors/persistence-mapping-error';
import {
  RoomCapacityError,
  RoomStateError,
  RoomParticipantNotFoundError,
} from './room.errors';
import { SeatPrimitives } from '../../entities/seat/contracts/seat.type';
import { RoomState } from '../../value-objects/rooms/contracts/room-state.enum';
import { RoomPrimitives, RoomPrimitivesWithState } from '../../value-objects/rooms/contracts/room.type';
import { ParticipantId } from 'value-objects/participants/participant-id/participant-id.vo';

export class Room {
  private readonly state: RoomState;
  private constructor(
    public readonly roomId: RoomId,
    public readonly config: RoomConfig,
    public readonly seats: Seat[],
    public readonly createdAt: Instant,
  ) {
    // Compute state
    let state: RoomState;

    if (this.size < this.config.minSeats) {
      state = RoomState.FILLING;
    } else if (
      this.size >= this.config.minSeats &&
      this.size < this.config.maxSeats
    ) {
      state = RoomState.READY;
    } else if (this.size === this.config.maxSeats) {
      state = RoomState.CLOSED;
    } else {
      state = RoomState.FILLING;
    }

    this.state = state;
  }

  static fromPrimitives(dto: RoomPrimitives): Room {
    try {
      ensure(
        dto,
        new PersistenceMappingError('Room DTO is null or undefined'),
      );

      ensure(
        dto.seats.length === dto.config.maxSeats,
        new PersistenceMappingError('Number of seats does not match max seats'),
      );

      ensure(
        dto.seats.every(
          (s) =>
            Number.isInteger(s.seatNo) &&
            s.seatNo >= 0 &&
            s.seatNo < dto.config.maxSeats,
        ),
        new PersistenceMappingError('Seat number out of range', {
          seatNos: dto.seats.map((s) => s.seatNo),
        }),
      );
      ensure(
        new Set(dto.seats.map((s) => s.seatNo)).size === dto.seats.length,
        new PersistenceMappingError('Duplicate seat numbers', {
          seatNos: dto.seats.map((s) => s.seatNo),
        }),
      );

      const roomId = RoomId.fromPrimitives(dto.roomId);
      const config = RoomConfig.fromPrimitives(dto.config);
      const createdAt = Instant.fromPrimitives(dto.createdAt);

      // Create seats with indices 0..maxSeats-1
      const seats: Seat[] = Array.from({ length: dto.config.maxSeats });
      for (const seatDto of dto.seats) {
        seats[seatDto.seatNo] = Seat.fromPrimitives(seatDto);
      }

      return new Room(roomId, config, seats, createdAt);
    } catch (error) {
      if (error instanceof PersistenceMappingError) {
        throw error;
      }
      throw new PersistenceMappingError('Failed to map room from primitives', {
        originalError: error,
      });
    }
  }

  static create(roomId: RoomId, config: RoomConfig): Room {
    // Create seats with indices 0..maxSeats-1
    const seats: Seat[] = [];
    for (let i = 0; i < config.maxSeats; i++) {
      const seatPrimitives: SeatPrimitives = {
        seatNo: i,
      };
      seats.push(Seat.fromPrimitives(seatPrimitives));
    }

    return new Room(roomId, config, seats, Instant.now());
  }

  toPrimitives(): RoomPrimitivesWithState {
    return {
      roomId: this.roomId.toPrimitives(),
      config: this.config.toPrimitives(),
      seats: this.seats.map((seat) => seat.toPrimitives()),
      state: this.state,
      createdAt: this.createdAt.toPrimitives(),
    };
  }

  assignParticipantToSeat(participantId: ParticipantId, at: Instant): Room {
    ensure(
      this.hasSpace, // Room is not full
      new RoomCapacityError('Room capacity exceeded', {
        roomId: this.roomId.toPrimitives(),
        currentSize: this.size,
        maxSeats: this.config.maxSeats,
        participantId: participantId.toPrimitives(),
      }),
    );

    ensure(
      !this.seats.some(
        (s) =>
          (s.isOccupied || s.isReservedForReconnect) &&
          s.currentParticipantId === participantId.toPrimitives(),
      ),
      new RoomStateError('Participant already in room', {
        roomId: this.roomId.toPrimitives(),
        participantId: participantId.toPrimitives(),
      }),
    );

    // Find first available seat
    const availableSeatIndex = this.seats.findIndex((seat) => seat.isEmpty);

    ensure(
      availableSeatIndex !== -1,
      new RoomCapacityError('Room capacity exceeded', {
        roomId: this.roomId.toPrimitives(),
        participantId: participantId.toPrimitives(),
        currentSize: this.size,
        maxSeats: this.config.maxSeats,
      }),
    );

    // Assign the seat
    const newSeats = [...this.seats];
    newSeats[availableSeatIndex] = this.seats[availableSeatIndex].assign(
      participantId,
      at,
    );

    return new Room(this.roomId, this.config, newSeats, this.createdAt);
  }

  releaseSeatOfParticipant(participantId: ParticipantId, at: Instant): Room {
    const seat = this.seats.find(
      (seat) => seat.currentParticipantId === participantId.toPrimitives(),
    );

    ensure(
      seat,
      new RoomParticipantNotFoundError('Participant not found', {
        roomId: this.roomId.toPrimitives(),
        participantId: participantId.toPrimitives(),
      }),
    );

    const updatedSeats = this.seats.map((s) =>
      s.currentParticipantId === participantId.toPrimitives() ? s.release(at) : s,
    );

    return new Room(this.roomId, this.config, updatedSeats, this.createdAt);
  }

  releaseAllReservedSeats(at: Instant): Room {
    const newSeats = this.seats.map((seat) =>
      seat.isReservedForReconnect ? seat.release(at) : seat,
    );

    return new Room(this.roomId, this.config, newSeats, this.createdAt);
  }

  reserveSeatForReconnect(participantId: ParticipantId, at: Instant): Room {
    const seatIndex = this.seats.findIndex(
      (seat) =>
        seat.isOccupied &&
        seat.currentParticipantId === participantId.toPrimitives(),
    );

    ensure(
      seatIndex !== -1,
      new RoomParticipantNotFoundError('Participant not found', {
        roomId: this.roomId.toPrimitives(),
        participantId: participantId.toPrimitives(),
      }),
    );

    const newSeats = [...this.seats];
    newSeats[seatIndex] = this.seats[seatIndex].reserveForReconnect(at);

    return new Room(this.roomId, this.config, newSeats, this.createdAt);
  }

  restoreSeatAfterReconnect(participantId: ParticipantId, at: Instant): Room {
    const seatIndex = this.seats.findIndex(
      (seat) =>
        seat.isReservedForReconnect &&
        seat.currentParticipantId === participantId.toPrimitives(),
    );

    ensure(
      seatIndex !== -1,
      new RoomParticipantNotFoundError(
        'Participant not found in reconnect state',
        {
          roomId: this.roomId.toPrimitives(),
          participantId: participantId.toPrimitives(),
        },
      ),
    );

    const newSeats = [...this.seats];

    newSeats[seatIndex] = this.seats[seatIndex].restoreAfterReconnect(
      participantId,
      at,
      this.config.disconnectionPolicy,
    );

    return new Room(this.roomId, this.config, newSeats, this.createdAt);
  }

  // Derived properties
  get size(): number {
    return this.seats.filter(
      (seat) => seat.isOccupied || seat.isReservedForReconnect,
    ).length;
  }

  get hasSpace(): boolean {
    return this.size < this.config.maxSeats;
  }

  get currentState(): RoomState {
    return this.state;
  }

  get isFilling(): boolean {
    return this.size < this.config.minSeats;
  }

  get isReady(): boolean {
    return (
      this.size >= this.config.minSeats && this.size < this.config.maxSeats
    );
  }

  get isClosed(): boolean {
    return this.size === this.config.maxSeats;
  }

  get occupiedSeats(): Seat[] {
    return this.seats.filter((seat) => seat.isOccupied);
  }

  get availableSeats(): Seat[] {
    return this.seats.filter((seat) => seat.isEmpty);
  }

  get reservedSeats(): Seat[] {
    return this.seats.filter((seat) => seat.isReservedForReconnect);
  }

  get allSeats(): Seat[] {
    return [...this.seats];
  }
}
