import { RoomConfigPrimitives } from 'value-objects/rooms/contracts/room-config';
import { PositiveInt } from '../../../primitives/positive-int/positive-int.primitive';
import { ensure } from '../../../support/ensure';
import { RoomConfigMinInvalidError, RoomConfigMaxInvalidError } from './room-config.errors';
import { DisconnectionPolicy } from '../../../policies/disconnection/disconnection-policy.vo';


export class RoomConfig {
  private constructor(
    private readonly _minSeats: number,
    private readonly _maxSeats: number,
    private readonly _avoidSingleton: boolean,
    private readonly _disconnectionPolicy: DisconnectionPolicy
  ) {}

  static fromPrimitives(raw: RoomConfigPrimitives): RoomConfig {
    const min = PositiveInt.fromPrimitives(raw.minSeats).toPrimitives();
    const max = PositiveInt.fromPrimitives(raw.maxSeats).toPrimitives();
    const disconnectionPolicy = DisconnectionPolicy.fromPrimitives(
        raw.disconnectionPolicy,
      );
    
    ensure(min >= 2, new RoomConfigMinInvalidError({ min }));
    ensure(max >= min, new RoomConfigMaxInvalidError({ min, max }));

    return new RoomConfig(
      min,
      max,
      raw.avoidSingleton,
      disconnectionPolicy,
    );
  }

  toPrimitives(): RoomConfigPrimitives {
    return {
      minSeats: this._minSeats,
      maxSeats: this._maxSeats,
      avoidSingleton: this._avoidSingleton,
      disconnectionPolicy: this._disconnectionPolicy.toPrimitives(),
    };
  }

  get minSeats(): number {
    return this._minSeats;
  }

  get maxSeats(): number {
    return this._maxSeats;
  }

  get avoidSingleton(): boolean {
    return this._avoidSingleton;
  }

  get disconnectionPolicy(): DisconnectionPolicy {
    return this._disconnectionPolicy;
  }
}
