import { SeatPrimitives, SeatPrimitivesWithState } from "entities/seat/contracts/seat.type";
import { RoomConfigPrimitives } from "value-objects/rooms/contracts/room-config";
import { RoomState } from "./room-state.enum";

export type RoomPrimitives = {
  roomId: string;
  config: RoomConfigPrimitives;
  seats: SeatPrimitives[];
  createdAt: number;
};

export type RoomPrimitivesWithState = {
  roomId: string;
  config: RoomConfigPrimitives;
  seats: SeatPrimitivesWithState[];
  createdAt: number;
  state: RoomState
}

