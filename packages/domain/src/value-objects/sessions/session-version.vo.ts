import { ensure } from '../../support/ensure';
import { DomainError } from '../../errors/domain-error';

// ============================================================================
// Session Version Value Object
// ============================================================================

export const ERR_SESSION_VERSION_INVALID = 'SESSION_VERSION.INVALID' as const;
export class SessionVersionInvalidError extends DomainError {
  constructor(ctx?: Record<string, unknown>) {
    super(ERR_SESSION_VERSION_INVALID, 'Session version must be a non-negative integer', ctx);
  }
}

export const ERR_SESSION_VERSION_CONFLICT = 'SESSION_VERSION.CONFLICT' as const;
export class SessionVersionConflictError extends DomainError {
  constructor(ctx?: Record<string, unknown>) {
    super(ERR_SESSION_VERSION_CONFLICT, 'Session version conflict - aggregate was modified by another operation', ctx);
  }
}

/**
 * Session Version Value Object
 * 
 * Represents the version of a session aggregate for optimistic concurrency control.
 * Version starts at 0 for new aggregates and increments with each successful mutation.
 */
export class SessionVersion {
  private constructor(private readonly value: number) {}

  static fromPrimitives(version: number): SessionVersion {
    ensure(
      Number.isInteger(version) && version >= 0,
      new SessionVersionInvalidError({ version })
    );

    return new SessionVersion(version);
  }

  static initial(): SessionVersion {
    return new SessionVersion(0);
  }

  toPrimitives(): number {
    return this.value;
  }

  /**
   * Increment version for next mutation
   */
  increment(): SessionVersion {
    return new SessionVersion(this.value + 1);
  }

  /**
   * Check if this version matches expected version for optimistic concurrency control
   */
  matches(expectedVersion: number): boolean {
    return this.value === expectedVersion;
  }

  /**
   * Validate that this version is the expected version, throw if not
   */
  validateExpected(expectedVersion: number): void {
    if (!this.matches(expectedVersion)) {
      throw new SessionVersionConflictError({
        currentVersion: this.value,
        expectedVersion
      });
    }
  }

  equals(other: SessionVersion): boolean {
    return this.value === other.value;
  }

  toString(): string {
    return `SessionVersion(${this.value})`;
  }
}
