import { SessionConfig } from '../session-config.vo';
import { SessionConfigInvalidError, ERR_VO_SESSION_CONFIG_INVALID } from '../session-config.errors';
import { SessionConfigPrimitives } from '../../contracts/session-config.type';
import { SessionMode } from '../../contracts/session-mode.enum';
import { LateJoinAllocationMode } from '../../../../policies/late-join/types/late-join-allocation-mode.enum';
import { AllocationStrategy } from '../../../../policies/autopilot/types/allocation-strategy.enum';

describe('SessionConfig', () => {
  const validPrimitives: SessionConfigPrimitives = {
    // Scheduling
    scheduledStartAt: 1640995200000, // 2022-01-01 00:00:00 UTC
    estimatedDurationMs: 3600000, // 1 hour
    
    // Rooms configuration
    defaultRoomConfig: {
      minSeats: 2,
      maxSeats: 8,
      avoidSingleton: true,
      disconnectionPolicy: { holdSeatForMs: 30000 },
    },
    maxParticipants: 50,
    
    // Rounds configuration
    defaultRoundDurationMs: 900000, // 15 minutes
    
    // Policies
    autopilotPolicy: {
      allocationStategy: AllocationStrategy.ROUND_ROBIN,
    },
    lobbyAdmissionPolicy: {
      allowEarlyJoinMs: 300000, // 5 minutes
      requireHostPresent: true
    },
    disconnectionPolicy: {
      holdSeatForMs: 120000, // 2 minutes
    },
    lateJoinPolicy: {
      allocationMode: LateJoinAllocationMode.BEST_FIT
    },
    
    // Session mode
    mode: SessionMode.HOSTED
  };

  describe('fromPrimitives - valid cases', () => {
    it('should accept valid complete configuration', () => {
      const composition = SessionConfig.fromPrimitives(validPrimitives);
      
      // Verify scheduling
      expect(composition.scheduledStartAt.toPrimitives()).toBe(validPrimitives.scheduledStartAt);
      expect(composition.estimatedDuration.toPrimitives()).toBe(validPrimitives.estimatedDurationMs);
      
      // Verify rooms
      expect(composition.defaultRoomConfig.minSeats).toBe(2);
      expect(composition.defaultRoomConfig.maxSeats).toBe(8);
      expect(composition.defaultRoomConfig.avoidSingleton).toBe(true);
      expect(composition.maxParticipants).toBe(50);
      
      // Verify rounds
      expect(composition.defaultRoundDurationMs).toBe(validPrimitives.defaultRoundDurationMs);
      
      // Verify policies
      expect(composition.lobbyAdmissionPolicy.requireHostPresent).toBe(true);
      expect(composition.disconnectionPolicy.holdSeatForMs).toBe(120000);
      expect(composition.lateJoinPolicy.allocationMode).toBe(LateJoinAllocationMode.BEST_FIT);
      
      // Verify session mode
      expect(composition.mode).toBe(SessionMode.HOSTED);
    });

    it('should accept maxParticipants equal to defaultRoomConfig.maxSeats', () => {
      const primitives = {
        ...validPrimitives,
        maxParticipants: 8,
        defaultRoomConfig: { minSeats: 2, maxSeats: 8, avoidSingleton: true, disconnectionPolicy: { holdSeatForMs: 30000 } }
      };
      
      const composition = SessionConfig.fromPrimitives(primitives);
      
      expect(composition.maxParticipants).toBe(8);
      expect(composition.defaultRoomConfig.maxSeats).toBe(8);
    });

    it('should accept defaultRoundDurationMs=30000', () => {
      const primitives = {
        ...validPrimitives,
        defaultRoundDurationMs: 30000
      };
      
      const composition = SessionConfig.fromPrimitives(primitives);
      
      expect(composition.defaultRoundDurationMs).toBe(30000);
    });
  });

  describe('fromPrimitives - invalid cases', () => {
    it('should reject defaultRoundDurationMs=0', () => {
      const primitives = {
        ...validPrimitives,
        defaultRoundDurationMs: 0
      };
      
      expect(() => SessionConfig.fromPrimitives(primitives)).toThrow(SessionConfigInvalidError);
      
      try {
        SessionConfig.fromPrimitives(primitives);
      } catch (error) {
        expect(error).toBeInstanceOf(SessionConfigInvalidError);
        expect((error as SessionConfigInvalidError).code).toBe(ERR_VO_SESSION_CONFIG_INVALID);
        expect((error as SessionConfigInvalidError).ctx?.defaultRoundDurationMs).toBe(0);
        expect((error as SessionConfigInvalidError).ctx?.reason).toBe('defaultRoundDurationMs must be > 0');
      }
    });
    it('should reject maxParticipants=0', () => {
      const primitives = {
        ...validPrimitives,
        maxParticipants: 0
      };
      
      expect(() => SessionConfig.fromPrimitives(primitives)).toThrow(SessionConfigInvalidError);
      
      try {
        SessionConfig.fromPrimitives(primitives);
      } catch (error) {
        expect(error).toBeInstanceOf(SessionConfigInvalidError);
        expect((error as SessionConfigInvalidError).code).toBe(ERR_VO_SESSION_CONFIG_INVALID);
        expect((error as SessionConfigInvalidError).ctx?.maxParticipants).toBe(0);
        expect((error as SessionConfigInvalidError).ctx?.reason).toBe('maxParticipants must be > 0');
      }
    });

    it('should reject negative defaultRoundDurationMs', () => {
      const primitives = {
        ...validPrimitives,
        defaultRoundDurationMs: -1
      };
      
      expect(() => SessionConfig.fromPrimitives(primitives)).toThrow(SessionConfigInvalidError);
      
      try {
        SessionConfig.fromPrimitives(primitives);
      } catch (error) {
        expect(error).toBeInstanceOf(SessionConfigInvalidError);
        expect((error as SessionConfigInvalidError).code).toBe(ERR_VO_SESSION_CONFIG_INVALID);
        expect((error as SessionConfigInvalidError).ctx?.defaultRoundDurationMs).toBe(-1);
        expect((error as SessionConfigInvalidError).ctx?.reason).toBe('defaultRoundDurationMs must be > 0');
      }
    });

    it('should reject maxParticipants < defaultRoomConfig.maxSeats', () => {
      const primitives = {
        ...validPrimitives,
        maxParticipants: 5,
        defaultRoomConfig: { minSeats: 2, maxSeats: 10, avoidSingleton: true, disconnectionPolicy: { holdSeatForMs: 30000 } }
      };
      
      expect(() => SessionConfig.fromPrimitives(primitives)).toThrow(SessionConfigInvalidError);
      
      try {
        SessionConfig.fromPrimitives(primitives);
      } catch (error) {
        expect(error).toBeInstanceOf(SessionConfigInvalidError);
        expect((error as SessionConfigInvalidError).code).toBe(ERR_VO_SESSION_CONFIG_INVALID);
        expect((error as SessionConfigInvalidError).ctx?.maxParticipants).toBe(5);
        expect((error as SessionConfigInvalidError).ctx?.defaultRoomConfigMaxSeats).toBe(10);
        expect((error as SessionConfigInvalidError).ctx?.reason).toBe('maxParticipants must be >= defaultRoomConfig.maxSeats');
      }
    });

    it('should reject invalid scheduledStartAt', () => {
      const primitives = {
        ...validPrimitives,
        scheduledStartAt: -1
      };
      
      expect(() => SessionConfig.fromPrimitives(primitives)).toThrow();
    });

    it('should reject invalid defaultRoomConfig', () => {
      const primitives = {
        ...validPrimitives,
        defaultRoomConfig: { minSeats: 1, maxSeats: 4, avoidSingleton: true, disconnectionPolicy: { holdSeatForMs: 30000 } } // minSeats < 2
      };
      
      expect(() => SessionConfig.fromPrimitives(primitives)).toThrow();
    });

    it('should reject invalid autopilotPolicy', () => {
      const primitives = {
        ...validPrimitives,
        autopilotPolicy: {
          allocationStategy: 'INVALID_STRATEGY' as AllocationStrategy,
        }
      };

      expect(() => SessionConfig.fromPrimitives(primitives)).toThrow();
    });
  });

  describe('toPrimitives', () => {
    it('should serialize correctly', () => {
      const composition = SessionConfig.fromPrimitives(validPrimitives);
      const serialized = composition.toPrimitives();
      
      expect(serialized).toEqual(validPrimitives);
    });
  });

  describe('roundtrip', () => {
    it('should maintain equality after roundtrip', () => {
      const composition = SessionConfig.fromPrimitives(validPrimitives);
      const roundtrip = SessionConfig.fromPrimitives(composition.toPrimitives());
      
      expect(roundtrip.toPrimitives()).toEqual(validPrimitives);
    });
  });
});
