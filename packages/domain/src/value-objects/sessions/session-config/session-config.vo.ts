
import { SessionMode } from '../contracts/session-mode.enum';

import { ensure } from '../../../support/ensure';
import { SessionConfigInvalidError } from './session-config.errors';
import { SessionConfigPrimitives } from 'value-objects/sessions/contracts/session-config.type';
import { AutopilotPolicy } from '../../../policies/autopilot/autopilot-policy.vo';
import { DisconnectionPolicy } from '../../../policies/disconnection/disconnection-policy.vo';
import { LateJoinPolicy } from '../../../policies/late-join/late-join-policy.vo';
import { LobbyAdmissionPolicy } from '../../../policies/lobby-admission/lobby-admission.vo';
import { Duration } from '../../../primitives/duration/duration.primitive';
import { Instant } from '../../../primitives/instant/instant.primitive';
import { RoomConfig } from '../../rooms/room-config/room-config.vo';

export class SessionConfig {
  private constructor(
    private readonly _scheduledStartAt: Instant,
    private readonly _estimatedDuration: Duration,
    private readonly _defaultRoomConfig: RoomConfig,
    private readonly _maxParticipants: number,
    private readonly _defaultRoundDurationMs: number,
    private readonly _autopilotPolicy: AutopilotPolicy,
    private readonly _lobbyAdmissionPolicy: LobbyAdmissionPolicy,
    private readonly _disconnectionPolicy: DisconnectionPolicy,
    private readonly _lateJoinPolicy: LateJoinPolicy,
    private readonly _mode: SessionMode,
  ) {}

  static fromPrimitives(raw: SessionConfigPrimitives): SessionConfig {
    // Validate and construct all components
    const scheduledStartAt = Instant.fromPrimitives(raw.scheduledStartAt);
    const estimatedDuration = Duration.fromPrimitives(raw.estimatedDurationMs);
    const defaultRoomConfig = RoomConfig.fromPrimitives(raw.defaultRoomConfig);
    const autopilotPolicy = AutopilotPolicy.fromPrimitives(raw.autopilotPolicy);
    const lobbyAdmissionPolicy = LobbyAdmissionPolicy.fromPrimitives(
      raw.lobbyAdmissionPolicy,
    );
    const reconnectionPolicy = DisconnectionPolicy.fromPrimitives(
      raw.disconnectionPolicy,
    );
    const lateJoinPolicy = LateJoinPolicy.fromPrimitives(raw.lateJoinPolicy);

    // Validate maxParticipants is positive
    ensure(
      raw.maxParticipants > 0,
      new SessionConfigInvalidError({
        maxParticipants: raw.maxParticipants,
        reason: 'maxParticipants must be > 0',
      }),
    );

    // Validate maxParticipants >= defaultRoomConfig.maxSeats
    ensure(
      raw.maxParticipants >= defaultRoomConfig.maxSeats,
      new SessionConfigInvalidError({
        maxParticipants: raw.maxParticipants,
        defaultRoomConfigMaxSeats: defaultRoomConfig.maxSeats,
        reason: 'maxParticipants must be >= defaultRoomConfig.maxSeats',
      }),
    );

    // Validate mode is valid
    ensure(
      raw.mode === SessionMode.HOSTED || raw.mode === SessionMode.AUTOPILOT,
      new SessionConfigInvalidError({
        mode: raw.mode,
        reason: 'mode must be HOSTED or AUTOPILOT',
      }),
    );

    // Validate default round duration is positive
    ensure(
      raw.defaultRoundDurationMs > 0,
      new SessionConfigInvalidError({
        defaultRoundDurationMs: raw.defaultRoundDurationMs,
        reason: 'defaultRoundDurationMs must be > 0',
      }),
    );

    return new SessionConfig(
      scheduledStartAt,
      estimatedDuration,
      defaultRoomConfig,
      raw.maxParticipants,
      raw.defaultRoundDurationMs,
      autopilotPolicy,
      lobbyAdmissionPolicy,
      reconnectionPolicy,
      lateJoinPolicy,
      raw.mode,
    );
  }

  toPrimitives(): SessionConfigPrimitives {
    return {
      scheduledStartAt: this._scheduledStartAt.toPrimitives(),
      estimatedDurationMs: this._estimatedDuration.toPrimitives(),
      defaultRoomConfig: this._defaultRoomConfig.toPrimitives(),
      maxParticipants: this._maxParticipants,
      defaultRoundDurationMs: this._defaultRoundDurationMs,
      autopilotPolicy: this._autopilotPolicy.toPrimitives(),
      lobbyAdmissionPolicy: this._lobbyAdmissionPolicy.toPrimitives(),
      disconnectionPolicy: this._disconnectionPolicy.toPrimitives(),
      lateJoinPolicy: this._lateJoinPolicy.toPrimitives(),
      mode: this._mode,
    };
  }

  // Scheduling getters
  get scheduledStartAt(): Instant {
    return this._scheduledStartAt;
  }

  get estimatedDuration(): Duration {
    return this._estimatedDuration;
  }

  // Rooms getters
  get defaultRoomConfig(): RoomConfig {
    return this._defaultRoomConfig;
  }

  get maxParticipants(): number {
    return this._maxParticipants;
  }

  // Policy getters
  get autopilotPolicy(): AutopilotPolicy {
    return this._autopilotPolicy;
  }

  get lobbyAdmissionPolicy(): LobbyAdmissionPolicy {
    return this._lobbyAdmissionPolicy;
  }

  get disconnectionPolicy(): DisconnectionPolicy {
    return this._disconnectionPolicy;
  }

  get lateJoinPolicy(): LateJoinPolicy {
    return this._lateJoinPolicy;
  }
  get defaultRoundDurationMs(): number {
    return this._defaultRoundDurationMs;
  }

  // Session mode getter
  get mode(): SessionMode {
    return this._mode;
  }
}
