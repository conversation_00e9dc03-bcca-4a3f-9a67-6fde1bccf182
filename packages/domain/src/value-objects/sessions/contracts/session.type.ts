import { SessionConfigPrimitives } from "./session-config.type";
import { ParticipantPresencePrimitives } from "entities/participant-presence/contracts/presence.type";
import { RoomPrimitives } from "value-objects/rooms/contracts/room.type";
import { SessionState } from "./session.state.enum";
import { RoundPrimitives } from "value-objects/sessions/round/contracts/round.type";

export type SessionPrimitives = {
  sessionId: string;
  config: SessionConfigPrimitives;
  createdByUserId: string;
  hostId?: string;
  mainRoomId: string;
  currentRoundNo: number;
  rounds: RoundPrimitives[];
  participants: ParticipantPresencePrimitives[];
  rooms: RoomPrimitives[];
  createdAt: number;
  state: SessionState;
  version?: number; // Optional for backward compatibility
};

