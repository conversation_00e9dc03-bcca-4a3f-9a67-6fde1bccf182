import { ensure } from '../../../support/ensure';
import { Instant } from '../../../primitives/instant/instant.primitive';
import { PersistenceMappingError } from '../../../errors/persistence-mapping-error';
import { RoundKind } from './contracts/round-kind.enum';
import { RoundPrimitives } from './contracts/round.type';
import { PositiveInt } from '../../../primitives/positive-int/positive-int.primitive';

export class Round {
  private constructor(
    public readonly roundNo: PositiveInt,
    public readonly kind: RoundKind,
    public readonly durationMs: number,
    public readonly questions: string[],
    private startedAt?: Instant,
    private endedAt?: Instant,
    private closedAt?: Instant,
  ) {}

  static fromPrimitives(dto: RoundPrimitives): Round {
    try {
      ensure(
        dto,
        new PersistenceMappingError('Round DTO is null or undefined'),
      );

      ensure(
        Number.isInteger(dto.roundNo) && dto.roundNo > 0,
        new PersistenceMappingError('Invalid round number', {
          roundNo: dto.roundNo,
        }),
      );

      ensure(
        Object.values(RoundKind).includes(dto.kind),
        new PersistenceMappingError('Invalid round kind', { kind: dto.kind }),
      );

      const startedAt = dto.startedAt
        ? Instant.fromPrimitives(dto.startedAt)
        : undefined;
      const endedAt = dto.endedAt
        ? Instant.fromPrimitives(dto.endedAt)
        : undefined;
      const closedAt = dto.closedAt
        ? Instant.fromPrimitives(dto.closedAt)
        : undefined;
      const roundNo = PositiveInt.fromPrimitives(dto.roundNo);

      return new Round(
        roundNo,
        dto.kind,
        dto.durationMs,
        dto.questions,
        startedAt,
        endedAt,
        closedAt,
      );
    } catch (error) {
      if (error instanceof PersistenceMappingError) {
        throw error;
      }
      throw new PersistenceMappingError('Failed to map round from primitives', {
        originalError: error,
      });
    }
  }

  toPrimitives(): RoundPrimitives {
    return {
      roundNo: this.roundNo.toPrimitives(),
      kind: this.kind,
      durationMs: this.durationMs,
      questions: this.questions,
      startedAt: this.startedAt?.toPrimitives(),
      endedAt: this.endedAt?.toPrimitives(),
      closedAt: this.closedAt?.toPrimitives(),
    };
  }
}
