# Spokenly Domain Layer Documentation

## Overview

This directory contains comprehensive documentation for the Spokenly domain layer, which models spoken conversation practice sessions using Domain-Driven Design principles.

## Documentation Structure

### Core Architecture
- **[Domain Layer Architecture](./domain-layer-architecture.md)** - Overall architecture, patterns, and design principles
- **[Domain Model](./domain-model.md)** - Detailed specification of entities, aggregates, value objects, and policies
- **[Commands and Events](./commands-and-events.md)** - Complete command and event definitions with type safety

### Legacy Documentation
- **[Legacy Domain Layer v0.0.0](./legacy-domain-layer-v0.0.0.md)** - Previous documentation for reference (outdated)

## Quick Start

### Understanding the Domain

The domain layer models **conversation practice sessions** with these key concepts:

1. **Sessions** - Main aggregates that coordinate all operations
2. **Rooms** - Manage seat allocation and capacity (main room + breakouts)
3. **Participants** - Track user presence and state throughout sessions
4. **Rounds** - Timed activities within sessions
5. **Policies** - Configurable business rules (autopilot, disconnection, etc.)

### Key Design Patterns

1. **Construction Pattern** - All domain objects use `fromPrimitives()` factory methods with validation
2. **Immutable Objects** - All domain objects are immutable; operations return new instances
3. **Event Sourcing** - Aggregates track uncommitted events for persistence
4. **Type Safety** - Comprehensive TypeScript typing with discriminated unions
5. **Error Handling** - Structured domain errors with rich context

### Architecture Layers

```
domain/
├── primitives/           # Foundation types (Uuid, Instant, Duration, etc.)
├── value-objects/        # Domain concepts (SessionConfig, RoomConfig, etc.)
├── policies/            # Business rules (AutopilotPolicy, DisconnectionPolicy, etc.)
├── entities/            # Objects with identity (Seat, ParticipantPresence)
├── aggregates/          # Consistency boundaries (Session, Room)
├── contracts/           # Public interfaces (commands, events)
├── errors/              # Domain error types
└── support/             # Utilities and helpers
```

## Key Features

### Session Management
- **Lifecycle States**: SCHEDULED → RUNNING → PAUSED → COMPLETED/CANCELED
- **Participant Management**: Join, leave, seat assignment, reconnection handling
- **Room Orchestration**: Main room + dynamic breakout room creation
- **Round Management**: Timed activities with configurable durations

### Business Rules (Policies)
- **AutopilotPolicy**: Automated participant allocation strategies
- **DisconnectionPolicy**: Seat reservation during temporary disconnections
- **LateJoinPolicy**: Mid-round participant placement strategies
- **LobbyAdmissionPolicy**: Pre-session join controls

### Type Safety
- **Discriminated Unions**: Type-safe command and event handling
- **Primitive Validation**: Comprehensive validation at construction time
- **Rich Error Types**: Structured errors with context for debugging

## Usage Examples

### Creating a Session
```typescript
const sessionId = SessionId.fromPrimitives('session-123');
const config = SessionConfig.fromPrimitives(configData);
const adminId = Uuid.fromPrimitives('admin-456');
const mainRoomId = RoomId.fromPrimitives('room-789');

const session = Session.create(sessionId, config, adminId, mainRoomId);
```

### Processing Commands
```typescript
// Commands are pure data structures
const command: StartSessionCommand = {
  kind: 'StartSession',
  version: 1,
  commandId: 'cmd-123',
  issuedAt: Date.now(),
  actor: { role: 'HOST' },
  payload: { sessionId: 'session-123' }
};

// Domain operations return new instances
const updatedSession = session.startSession();
const events = updatedSession.getUncommittedEvents();
```

### Working with Events
```typescript
// Events are emitted by domain operations
const events = session.getUncommittedEvents();
events.forEach(event => {
  console.log(`Event: ${event.kind}`, event.payload);
});
```

## Integration with Application Layer

The domain layer is pure business logic with no infrastructure dependencies. It integrates with the application layer through:

1. **Commands** - Application layer sends commands to domain aggregates
2. **Events** - Domain aggregates emit events for application layer to handle
3. **Primitives** - Data crosses boundaries as serializable primitive types
4. **Repositories** - Application layer persists/loads aggregates via primitive serialization

## Testing

The domain layer is designed for comprehensive testing:

- **Pure Functions** - All operations are deterministic
- **Immutable Objects** - No side effects or hidden state
- **Rich Assertions** - Domain invariants are explicitly enforced
- **Event Verification** - Test expected events are emitted

## Contributing

When modifying the domain layer:

1. **Follow Construction Patterns** - Use `fromPrimitives()` factories with validation
2. **Maintain Immutability** - Never modify existing objects
3. **Emit Appropriate Events** - All state changes should produce events
4. **Add Comprehensive Tests** - Cover both happy paths and error cases
5. **Update Documentation** - Keep documentation in sync with implementation

## Related Documentation

- [Application Layer Documentation](../../application/docs/README.md) - How the application layer orchestrates domain operations
- [Infrastructure Documentation](../../infrastructure/docs/README.md) - Persistence and external integrations (if available)

This domain layer provides a solid foundation for modeling conversation practice sessions with clear boundaries, consistent patterns, and robust validation.
