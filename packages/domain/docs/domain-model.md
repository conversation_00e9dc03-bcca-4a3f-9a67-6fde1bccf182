# Spokenly Domain Model

## Core Entities and Aggregates

### Session Aggregate

The `Session` is the main aggregate root that coordinates all session operations.

#### State Management
```typescript
enum SessionState {
  SCHEDULED = 'SCHEDULED',  // Created but not started
  RUNNING = 'RUNNING',      // Active session
  PAUSED = 'PAUSED',        // Temporarily suspended
  COMPLETED = 'COMPLETED',  // Successfully finished
  CANCELED = 'CANCELED'     // Terminated early
}
```

#### Key Properties
- `sessionId: SessionId` - Unique identifier
- `config: SessionConfig` - Complete session configuration
- `state: SessionState` - Current lifecycle state
- `createdByUserId: Uuid` - Session creator
- `hostId?: ParticipantId` - Current session host
- `mainRoomId: RoomId` - Primary room identifier
- `rounds: Round[]` - Session agenda
- `participants: ParticipantPresence[]` - Live participant tracking
- `rooms: Room[]` - All session rooms (main + breakouts)
- `currentRoundNo: NonNegativeInt` - Active round index

#### Core Operations
- `create()` - Factory method for new sessions
- `startSession()` - Begin the session (SCHEDULED → RUNNING)
- `pauseSession()` / `resumeSession()` - Suspend/resume operations
- `completeSession()` / `cancelSession()` - End session
- `addParticipant()` / `joinParticipant()` - Participant management
- `createBreakoutRoom()` - Dynamic room creation
- `assignParticipantToRoom()` - Move participants between rooms

### Room Aggregate

The `Room` manages seat allocation and capacity within a single room.

#### State Management
```typescript
enum RoomState {
  FILLING = 'FILLING',    // Below minimum capacity
  READY = 'READY',        // Between min and max capacity
  CLOSED = 'CLOSED'       // At maximum capacity
}
```

#### Key Properties
- `roomId: RoomId` - Unique identifier
- `config: RoomConfig` - Capacity and policies
- `seats: Seat[]` - All seats in the room
- `createdAt: Instant` - Creation timestamp

#### Derived Properties
- `size: number` - Current occupied + reserved seats
- `hasSpace: boolean` - Can accept more participants
- `currentState: RoomState` - Computed from size vs config

#### Core Operations
- `create()` - Factory method with seat initialization
- `assignParticipantToSeat()` - Allocate seat to participant
- `releaseSeatOfParticipant()` - Free participant's seat
- `reserveSeatForReconnect()` - Hold seat during disconnection
- `restoreSeatAfterReconnect()` - Restore reserved seat

### Seat Entity

Individual seat within a room with assignment lifecycle.

#### State Management
```typescript
enum SeatState {
  EMPTY = 'EMPTY',                          // Available for assignment
  OCCUPIED = 'OCCUPIED',                    // Currently assigned
  RESERVED_FOR_RECONNECT = 'RESERVED_FOR_RECONNECT'  // Held for reconnection
}
```

#### Key Properties
- `seatNo: SeatNo` - Zero-based index (identity)
- `participantId?: ParticipantId` - Current occupant
- `assignedAt?: Instant` - Assignment timestamp
- `reservedSince?: Instant` - Reservation start time
- `releasedAt?: Instant` - Last release time

#### Core Operations
- `assign()` - Assign to participant (EMPTY → OCCUPIED)
- `release()` - Free the seat (any state → EMPTY)
- `reserveForReconnect()` - Hold for reconnection (OCCUPIED → RESERVED)
- `restoreAfterReconnect()` - Restore within deadline (RESERVED → OCCUPIED)

### ParticipantPresence Entity

Tracks participant state throughout the session.

#### Key Properties
- `participantId: ParticipantId` - Unique identifier
- `tags: PresenceTags` - Role and metadata
- `joinedAt?: Instant` - First join time
- `leftAt?: Instant` - Last leave time
- `currentRoomId?: RoomId` - Current room location

#### Core Operations
- `create()` - Factory for new participants
- `onJoin()` / `onSoftLeave()` / `onLeave()` - Session lifecycle
- `enterRoom()` / `exitRoom()` - Room transitions
- `updateTags()` - Modify participant metadata

## Value Objects

### SessionConfig

Complete session configuration including all policies.

#### Key Components
- **Scheduling**: `scheduledStartAt`, `estimatedDurationMs`
- **Capacity**: `maxParticipants`, `defaultRoomConfig`
- **Rounds**: `defaultRoundDurationMs`
- **Policies**: All business rule configurations
- **Mode**: `HOSTED` vs `AUTOPILOT`

### RoomConfig

Room-specific capacity and behavior settings.

#### Properties
- `minSeats: number` - Minimum for READY state (≥ 2)
- `maxSeats: number` - Maximum capacity (≥ minSeats)
- `avoidSingleton: boolean` - Allocation preference
- `disconnectionPolicy: DisconnectionPolicy` - Reconnection rules

### Round

Round specification and execution state.

#### Properties
- `roundNo: PositiveInt` - Round number (1-based)
- `kind: RoundKind` - ICE_BREAKER | MAIN_TOPIC | FREE_TALK
- `durationMs: number` - Planned duration
- `questions: string[]` - Discussion prompts
- `startedAt?: Instant` - Actual start time
- `endedAt?: Instant` - Actual end time

## Policies

### AutopilotPolicy

Participant allocation strategy for automated sessions.

```typescript
enum AllocationStrategy {
  ROUND_ROBIN = 'ROUND_ROBIN',              // Distribute evenly
  FILL_SMALLEST_FIRST = 'FILL_SMALLEST_FIRST'  // Fill rooms to capacity
}
```

### DisconnectionPolicy

Seat reservation behavior during disconnections.

```typescript
type DisconnectionPolicyPrimitives = {
  holdSeatForMs: number;  // Reservation timeout (≥ 0)
}
```

### LateJoinPolicy

Mid-round participant placement strategy.

```typescript
enum LateJoinAllocationMode {
  BEST_FIT = 'BEST_FIT',        // Fill room with most space
  NEW_ROOM = 'NEW_ROOM',        // Always create new room
  LEAST_RECENT = 'LEAST_RECENT' // Rotate assignments fairly
}
```

### LobbyAdmissionPolicy

Pre-session join controls.

```typescript
type LobbyAdmissionPolicyPrimitives = {
  allowEarlyJoinMs: number;     // How early participants can join
  requireHostPresent: boolean;  // Must host be present to join
}
```

## Primitive Types

### Core Primitives
- `Uuid` - RFC4122 v4 validation with regex
- `Instant` - Non-negative integer timestamps (ms since epoch)
- `Duration` - Non-negative integer durations (ms)
- `PositiveInt` - Integers > 0
- `NonNegativeInt` - Integers ≥ 0
- `SeatNo` - Non-negative integers for seat indices
- `NonEmptyString` - Trimmed, non-empty strings

### Validation Examples
```typescript
// UUID validation
const UUID_V4_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

// Instant validation
ensure(Number.isInteger(raw) && raw >= 0 && Number.isFinite(raw));

// PositiveInt validation
ensure(Number.isInteger(raw) && raw > 0);
```

## Error Handling

### Error Hierarchy
- `DomainError` - Base class with code and context
- Module-specific errors (e.g., `SessionStateError`, `SeatStateError`)
- `PersistenceMappingError` - For `fromPrimitives()` failures

### Error Context
All errors include structured context for debugging:
```typescript
new SessionStateError('Cannot start session unless in SCHEDULED state', {
  currentState: this._state,
  sessionId: this._sessionId.toPrimitives(),
});
```

This domain model provides a comprehensive foundation for modeling conversation practice sessions with clear boundaries, consistent patterns, and robust validation.
