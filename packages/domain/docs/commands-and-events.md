# Commands and Events

## Overview

The domain layer defines pure command and event types without infrastructure concerns. Commands represent intentions to change state, while events represent facts about what happened.

## Command Structure

### Base Command Interface
```typescript
interface BaseCommand {
  readonly kind: string;                    // Command type identifier
  readonly version: number;                 // Schema version
  readonly expectedVersion?: number;        // Optimistic concurrency control
  readonly commandId: string;              // Deduplication key
  readonly correlationId?: string;         // Request correlation
  readonly causationId?: string;           // Parent command/event ID
  readonly issuedAt: number;               // Timestamp (Instant)
  readonly actor: Actor;                   // Who issued the command
}

interface Actor {
  role: ActorRole;                         // Permission level
  actorId?: string;                        // Actor identifier
}

enum ActorRole {
  SYSTEM = 'SYSTEM',
  HOST = 'HOST',
  CO_HOST = 'CO_HOST',
  ADMIN = 'ADMIN',
  ATTENDEE = 'ATTENDEE',
}
```

## Command Categories

### Session Lifecycle Commands

#### CreateSessionCommand
```typescript
interface CreateSessionCommand extends BaseCommand {
  kind: 'CreateSession';
  payload: {
    sessionId: string;
    config: SessionConfigPrimitives;
    createdByUserId: string;
    mainRoomId: string;
  };
}
```

#### Session State Commands
- `StartSessionCommand` - Begin session (SCHEDULED → RUNNING)
- `PauseSessionCommand` - Suspend session (RUNNING → PAUSED)
- `ResumeSessionCommand` - Resume session (PAUSED → RUNNING)
- `CompleteSessionCommand` - End successfully (RUNNING → COMPLETED)
- `CancelSessionCommand` - Terminate early (any → CANCELED)

### Round Management Commands

#### AddRoundCommand
```typescript
interface AddRoundCommand extends BaseCommand {
  kind: 'AddRound';
  payload: {
    sessionId: string;
    round: RoundPrimitives;
  };
}
```

#### Round Control Commands
- `StartRoundCommand` - Begin specific round
- `EndCurrentRoundCommand` - End active round

### Participant Management Commands

#### AddParticipantCommand
```typescript
interface AddParticipantCommand extends BaseCommand {
  kind: 'AddParticipant';
  payload: {
    sessionId: string;
    participantId: string;
    role: ParticipantRole;
  };
}
```

#### Participant Actions
- `JoinParticipantCommand` - Join session
- `SoftLeaveParticipantCommand` - Leave but maintain presence
- `HardLeaveParticipantCommand` - Complete removal
- `SeatParticipantCommand` - Assign to room/seat
- `RestoreParticipantCommand` - Restore after disconnection

### Room Management Commands

#### CreateBreakoutRoomCommand
```typescript
interface CreateBreakoutRoomCommand extends BaseCommand {
  kind: 'CreateBreakoutRoom';
  payload: {
    sessionId: string;
    roomId: string;
  };
}
```

#### Room Operations
- `AssignParticipantToRoomCommand` - Move participant
- `ReleaseSeatByParticipantCommand` - Free participant's seat
- `RotateBreakoutsCommand` - Shuffle room assignments
- `ReleaseAllReservedSeatsCommand` - Clear all reservations

### Host Management Commands

#### SetHostCommand
```typescript
interface SetHostCommand extends BaseCommand {
  kind: 'SetHost';
  payload: {
    sessionId: string;
    hostId: string;
  };
}
```

## Event Structure

### Base Event Interface
```typescript
interface BaseEvent {
  readonly kind: string;                    // Event type identifier
  readonly version: number;                 // Schema version
  readonly correlationId?: string;         // Request correlation
  readonly causationId?: string;           // Causing command/event ID
  readonly occurredAt: number;             // Timestamp (Instant)
}
```

## Event Categories

### Session Lifecycle Events

#### SessionCreatedEvent
```typescript
interface SessionCreatedEvent extends BaseEvent {
  kind: 'SessionCreated';
  payload: {
    sessionId: string;
    createdByUserId: string;
  };
}
```

#### Session State Events
- `SessionStartedEvent` - Session began
- `SessionPausedEvent` - Session suspended
- `SessionResumedEvent` - Session resumed
- `SessionCompletedEvent` - Session ended successfully
- `SessionCanceledEvent` - Session terminated

### Round Management Events

#### RoundAddedToSessionEvent
```typescript
interface RoundAddedToSessionEvent extends BaseEvent {
  kind: 'RoundAddedToSession';
  payload: {
    sessionId: string;
    round: RoundPrimitives;
  };
}
```

#### Round Lifecycle Events
- `RoundStartedEvent` - Round began
- `RoundEndedEvent` - Round completed

### Participant Management Events

#### ParticipantAddedToSessionEvent
```typescript
interface ParticipantAddedToSessionEvent extends BaseEvent {
  kind: 'ParticipantAddedToSession';
  payload: {
    sessionId: string;
    participantId: string;
    role: ParticipantRole;
  };
}
```

#### Participant State Events
- `ParticipantJoinedEvent` - Participant joined session
- `ParticipantSoftLeftEvent` - Participant left but maintained presence
- `ParticipantHardLeftEvent` - Participant completely removed
- `ParticipantSeatedEvent` - Participant assigned to seat
- `ParticipantRestoredEvent` - Participant restored after disconnection

### Room Management Events

#### BreakoutRoomCreatedEvent
```typescript
interface BreakoutRoomCreatedEvent extends BaseEvent {
  kind: 'BreakoutRoomCreated';
  payload: {
    sessionId: string;
    roomId: string;
  };
}
```

#### Room Operation Events
- `ParticipantAssignedToRoomEvent` - Participant moved to room
- `SeatReleasedByParticipantEvent` - Seat freed
- `BreakoutRoomsRotatedEvent` - Rooms shuffled
- `AllReservedSeatsReleasedEvent` - All reservations cleared
- `ParticipantsDistributedEvent` - Participants allocated to breakouts
- `ParticipantsReturnedToMainEvent` - Participants returned to main room

### Host Management Events

#### HostSetEvent
```typescript
interface HostSetEvent extends BaseEvent {
  kind: 'HostSet';
  payload: {
    sessionId: string;
    hostId: string;
  };
}
```

## Type Safety and Utilities

### Discriminated Unions
```typescript
export type DomainCommand =
  | CreateSessionCommand
  | StartSessionCommand
  | PauseSessionCommand
  // ... all command types

export type DomainEvent =
  | SessionCreatedEvent
  | SessionStartedEvent
  | SessionPausedEvent
  // ... all event types
```

### Type Guards
```typescript
// Check if object is a valid domain command
function isDomainCommand(candidate: unknown): candidate is DomainCommand

// Check if command is of specific kind
function isCommandOfKind<K extends CommandKind>(
  command: DomainCommand,
  kind: K,
): command is Extract<DomainCommand, { kind: K }>
```

### Helper Functions
```typescript
// Create command with proper typing
function createCommand<K extends CommandKind>(
  kind: K,
  payload: CommandPayload<K>,
  options: CommandOptions,
): Extract<DomainCommand, { kind: K }>

// Create event with proper typing
function createEvent<K extends EventKind>(
  kind: K,
  payload: EventPayload<K>,
  metadata: EventMetadata,
): Extract<DomainEvent, { kind: K }>
```

## Design Principles

### 1. Pure Domain Types
Commands and events contain only domain data - no infrastructure concerns.

### 2. Immutable Structures
All command and event properties are readonly.

### 3. Rich Typing
Discriminated unions and generics ensure compile-time type safety.

### 4. Correlation Support
Built-in support for request correlation and causation tracking.

### 5. Versioning Ready
Version fields support schema evolution.

This command and event system provides a robust foundation for CQRS and event sourcing patterns while maintaining strong typing and clear domain boundaries.
