# Legacy Domain Layer Documentation (v0.0.0)

> **Note**: This is the legacy documentation that was previously located in the application package. 
> It has been moved here for reference but should be considered outdated.
> Please refer to the new domain layer documentation files for current implementation details.

# Spokenly Domain Layer (v0.2)

## 1. Overview & Goals

The domain layer models **spoken conversation practice sessions**. It manages participants, rooms, seats, and rounds, enforcing rules and producing domain events.

**Goals:**

* Deterministic & predictable breakout orchestration.
* Clear separation between **HOSTED** (manual host actions) and **AUTOPILOT** (system mirrors host actions).
* Simple, explicit lifecycles.
* Idempotent commands and consistent events.

---

## 2. Glossary

| Term              | Definition                                                                                                                                 |
| ----------------- | ------------------------------------------------------------------------------------------------------------------------------------------ |
| **Session**       | A scheduled conversation event with rounds and rooms.                                                                                      |
| **Round**         | A timed activity inside a session.                                                                                                         |
| **Main Room**     | Plenary room where all start/return in Hosted mode.                                                                                        |
| **Breakout Room** | Temporary grouping of participants.                                                                                                        |
| **Seat**          | A slot assigned to one participant.                                                                                                        |
| **Allocator**     | Logic deciding room creation and seat assignment.                                                                                          |
| **Room Status**   | `FILLING → READY → CLOSED` lifecycle. READY = valid (≥ min seats) but still accepts joiners until max. CLOSED = full or explicitly locked. |
| **SessionMode**   | `'HOSTED'` (manual actions) or `'AUTOPILOT'` (system automates host actions).                                                              |

---

## 3. Primitives

| Primitive          | Purpose                                        | Validation                | Errors (Cause)                                                    |
| ------------------ | ---------------------------------------------- | ------------------------- | ----------------------------------------------------------------- |
| **Uuid**           | Unique identifiers across aggregates/events.   | Must match UUID v4 regex. | `PRIMITIVE_INVALID_UUID` – string missing/invalid UUID format.    |
| **Instant**        | UTC timestamp in ms for ordering domain facts. | Finite integer ≥ 0.       | `PRIMITIVE_INVALID_INSTANT` – NaN, negative, or non-integer.      |
| **Duration**       | Time spans (session/round/grace).              | Integer ≥ 0.              | `PRIMITIVE_NEGATIVE_DURATION` – negative duration provided.       |
| **PositiveInt**    | Counts that cannot be zero (e.g., minSeats).   | Integer > 0.              | `PRIMITIVE_NONPOSITIVE_INT` – zero or negative.                   |
| **NonNegativeInt** | Indexes/counters that allow zero.              | Integer ≥ 0.              | `PRIMITIVE_NEGATIVE_INT` – negative.                              |
| **SeatNo**         | Seat index within a room.                      | Integer ≥ 0.              | `PRIMITIVE_INVALID_SEATNO` – negative/NaN; alias of negative int. |

---

This legacy documentation covered extensive details about:

- Domain policies (RoomConfig, LateJoinPolicy, etc.)
- Entity specifications (Seat, Room, Session)
- Aggregate behaviors and invariants
- Progressive allocator algorithms
- Event sourcing patterns
- Construction patterns and error handling

The legacy documentation has been superseded by the new domain layer architecture documentation. Please refer to:

- `domain-layer-architecture.md` - Overall architecture and design principles
- `domain-model.md` - Detailed domain model specification
- `commands-and-events.md` - Command and event definitions

The new documentation reflects the actual current implementation rather than the original design specifications.
