
## 1) Envelopes — what they are (in plain words)

**Why:** we wrap every domain command/event so we can dedupe retries, trace what caused what, and log cleanly.

**CommandEnvelope (simple):**

```ts
{
  commandId: Uuid,         // unique per action; dedupe key
  correlationId: Uuid,     // groups everything from one action (thread id)
  causationId?: Uuid,      // id of the parent command/event
  issuedAt: Instant,       // server time (ms)
  actor: { role: 'SYSTEM'|'HOST'|'CO_HOST'|'ADMIN'|'ATTENDEE', participantId?: Uuid },
  payload: DomainCommand   // the actual domain input (pure)
}
```

**EventEnvelope (simple):**

```ts
{
  eventId: Uuid,
  sessionId: Uuid,
  roundId?: Uuid,
  roomId?: Uuid,
  occurredAt: Instant,     // server time (ms)
  causationId?: Uuid,      // producing command/event id
  correlationId: Uuid,     // copied from the command
  kind: string,            // e.g. 'SeatAssigned'
  payload: DomainEvent,
  version: 1
}
```

**Defaults to remember:**

* If you forget `correlationId`: set it to the `commandId`.
* Use **server** time for `issuedAt` / `occurredAt`, not client time.
* IDs must be UUID v4.

---

## 2) Correlation — the simplest rule that works

**Goal:** one id to grep to see *everything* that happened because of a single trigger.

**Create a new `correlationId` when:**

* A **user** clicks a button (root action).
* A **timer** fires (round/grace/reconnect).
* An **external** webhook arrives.

**Reuse (copy) the existing `correlationId` when:**

* You’re doing something **because of** a previous event/command.

**Super-short rule:**

* **Have a parent?** copy parent’s `correlationId`.
* **No parent?** make a new one.
* **Forgot?** use `commandId` as `correlationId`.

**Tiny example:**

```
Host clicks “Start Round” → startRound (corr=A)
→ RoundStarted (corr=A)
→ createOrFillBreakoutRoom (corr=A)
→ SeatAssigned (corr=A)
```

---

## 3) Idempotency — how we avoid duplicates

**Why:** networks retry; we don’t want double seating or double moves.

**Rules:**

* Every command **must** have a unique `commandId`.
* If the same `commandId` arrives again → **do nothing** additional and emit `IdempotencyReplayIgnored`.
* Keep a simple **ledger** `{ commandId -> applied }`.

**Natural guards already in domain help:**

* “Round already ACTIVE/CLOSED” → no new transition.
* “Participant already seated” → no duplicate seat.

**Gotchas to avoid:**

* Don’t generate a new `commandId` on retry — use the **same** one.
* Provider adapter calls must accept `commandId` and be safe to retry.

---

## 4) Timers — only three kinds, remember them like this

1. **Round Timer**
   When we start a round, we set a timer for the planned duration.
   On expiry → we call `endRound`.

2. **Grace Timer**
   When a round ends, we set a grace timer (`effectiveGrace`).
   On expiry → Hosted: `returnAllParticipantsToMain`; Autopilot: reseat.

3. **Reconnect Timer** (per seat)
   When someone disconnects, we hold their seat for `holdSeatForMs`.
   On expiry → free the seat (`SeatReconnectionClosed`).

**Always: server time.**
**Pause/Resume:** store remaining ms on pause; re-arm with remaining on resume.
**Restart:** rebuild timers from saved deadlines; if overdue, fire immediately.

---

## 5) Provider (VideoSDK) — the tiny mental model

* **Each breakout room = one VideoSDK meeting.**
* Main room = one VideoSDK meeting too.

**Adapter calls (must be retry-safe by `commandId`):**

```ts
createRoom(roomId, commandId) -> { meetingId }
seatParticipant(roomId, participantId, commandId) -> { meetingId, joinToken }
moveParticipant(fromRoomId, toRoomId, participantId, commandId) -> { toMeetingId, joinToken }
releaseSeat(roomId, participantId, commandId) -> void
```

**Client flow (what the app does on events):**

* `RoomCreated` → cache `meetingId`.
* `SeatAssigned` → get `joinToken` + `meetingId`, join that meeting.
* `ParticipantMoved` → leave old meeting, join new with new token.
* `ParticipantsReturnedToMain` → join Main meeting.

**If adapter fails:** emit `SeatAssignmentFailed { reason }` once per `commandId`.

---

## 6) Logging — one format to rule them all

**Include these keys in every log line:**

* `correlationId`, `commandId` / `eventId`
* `sessionId`, `roundId`, `roomId` (when present)
* `commandName` / `eventKind`, `actor.role`

**Example:**

```
INFO correlation=corr-A cmd=c-1 name=startRound session=s-1
INFO correlation=corr-A evt=e-9  kind=RoundStarted session=s-1 round=rd-2
INFO correlation=corr-A evt=e-12 kind=SeatAssigned room=rm-7 seat=1 user=u-42
```

---

## 7) “Do / Don’t” cheatsheet

**Do**

* Do create a **new** `correlationId` at root triggers (user/timer/webhook).
* Do **copy** the parent `correlationId` for derived work.
* Do use **the same** `commandId` when retrying.
* Do use **server** time only.
* Do make adapter calls **idempotent by `commandId`**.

**Don’t**

* Don’t put correlation logic inside domain entities. (It’s app-layer.)
* Don’t create new rooms in the provider without a matching `RoomCreated` event.
* Don’t use client timestamps for timers or ordering.
