# Spokenly Domain Layer Architecture

## Overview

The Spokenly domain layer models **spoken conversation practice sessions** using Domain-Driven Design principles. It manages participants, rooms, seats, rounds, and session orchestration while enforcing business rules and producing domain events.

## Core Goals

- **Deterministic & predictable** session orchestration
- **Clear separation** between HOSTED (manual host actions) and AUTOPILOT (system-driven) modes
- **Simple, explicit lifecycles** for all entities
- **Idempotent commands** and consistent events
- **Type-safe construction** with validation at boundaries

## Architecture Patterns

### 1. Construction Pattern (Unified Factories)

Every domain type follows a consistent construction pattern:

```typescript
// Private constructor forces validation
class ValueObject {
  private constructor(private readonly value: T) {}
  
  // Factory method with validation
  static fromPrimitives(raw: Primitives): ValueObject {
    // Validation logic here
    return new ValueObject(validated);
  }
  
  // Serialization
  toPrimitives(): Primitives {
    return this.value;
  }
}
```

### 2. Error Handling Strategy

- **Leaf errors**: Specific error types per domain concept
- **Structured context**: Rich error information for debugging
- **Fail-fast validation**: Errors thrown at construction time
- **No silent failures**: All validation errors are explicit

### 3. Event Sourcing Support

- **Uncommitted events**: Aggregates track events until persistence
- **Event creation helpers**: Type-safe event construction
- **Correlation tracking**: Events carry causation and correlation IDs

## Domain Model Structure

### Primitives Layer
Foundation types with validation:
- `Uuid` - RFC4122 v4 identifiers
- `Instant` - UTC timestamps (ms since epoch)
- `Duration` - Non-negative time spans
- `PositiveInt` / `NonNegativeInt` - Validated integers
- `SeatNo` - Zero-based seat indices
- `NonEmptyString` - Trimmed, non-empty strings

### Value Objects Layer
Domain concepts without identity:
- `SessionConfig` - Complete session configuration
- `RoomConfig` - Room capacity and policies
- `SessionId` / `RoomId` / `ParticipantId` - Typed identifiers
- `DisplayName` - Participant display names
- `Round` - Round specifications and state

### Policies Layer
Configurable business rules:
- `AutopilotPolicy` - Participant allocation strategies
- `DisconnectionPolicy` - Seat reservation timeouts
- `LateJoinPolicy` - Mid-round participant placement
- `LobbyAdmissionPolicy` - Pre-session join rules

### Entities Layer
Objects with identity and lifecycle:
- `Seat` - Individual seat with assignment state
- `ParticipantPresence` - Participant session state tracking

### Aggregates Layer
Consistency boundaries and business logic:
- `Session` - Main aggregate coordinating all session operations
- `Room` - Manages seat allocation and room state

## Key Design Decisions

### 1. Immutable Domain Objects
All domain objects are immutable. State changes return new instances.

### 2. Computed State Properties
State is computed from data rather than stored separately:
```typescript
get currentState(): SeatState {
  if (this.reservedSince && this.participantId && this.assignedAt) {
    return SeatState.RESERVED_FOR_RECONNECT;
  } else if (this.participantId && this.assignedAt && !this.reservedSince) {
    return SeatState.OCCUPIED;
  } else {
    return SeatState.EMPTY;
  }
}
```

### 3. Rich Domain Events
Events carry complete context for downstream processing:
```typescript
interface SessionCreatedEvent extends BaseEvent {
  readonly kind: 'SessionCreated';
  readonly payload: {
    readonly sessionId: string;
    readonly createdByUserId: string;
  };
}
```

### 4. Type-Safe Command/Event Unions
Discriminated unions ensure type safety:
```typescript
export type DomainCommand =
  | CreateSessionCommand
  | StartSessionCommand
  | PauseSessionCommand
  // ... other command types
```

## File Organization

```
domain/
├── primitives/           # Foundation types
│   ├── uuid/
│   ├── instant/
│   ├── duration/
│   └── ...
├── value-objects/        # Domain concepts
│   ├── sessions/
│   ├── rooms/
│   ├── participants/
│   └── ...
├── policies/            # Business rules
│   ├── autopilot/
│   ├── disconnection/
│   └── ...
├── entities/            # Objects with identity
│   ├── seat/
│   └── participant-presence/
├── aggregates/          # Consistency boundaries
│   ├── session/
│   └── room/
├── contracts/           # Public interfaces
│   ├── commands/
│   └── events/
├── errors/              # Base error types
└── support/             # Utilities
```

## Next Steps

This architecture provides a solid foundation for:
- **Testability**: Pure functions and immutable objects
- **Maintainability**: Clear separation of concerns
- **Extensibility**: New features follow established patterns
- **Reliability**: Comprehensive validation and error handling

For implementation details, see the specific documentation for each layer component.
