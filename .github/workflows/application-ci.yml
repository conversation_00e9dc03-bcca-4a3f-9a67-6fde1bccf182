name: CI - Application Layer

on:
  pull_request:
    branches: [develop, main]
    paths:
      - 'packages/application/**'
      - 'pnpm-lock.yaml'
      - '.github/workflows/**'

  push:
    branches: [develop, main]
    paths:
      - 'packages/application/**'
      - 'pnpm-lock.yaml'
      - '.github/workflows/**'

jobs:
  application:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js (LTS 20.x)
        uses: actions/setup-node@v4
        with:
          node-version: 20.x

      - name: Setup pnpm & install (Application Layer)
        uses: pnpm/action-setup@v2
        with:
          version: 8
          run_install: true
          args: >-
            --frozen-lockfile
            --filter ./packages/application...

      - name: <PERSON><PERSON> (Application Layer)
        run: pnpm --filter ./packages/application lint

      - name: Test (Application Layer)
        run: pnpm --filter ./packages/application test

      - name: Build (Application Layer)
        run: pnpm --filter ./packages/application build

      - name: Upload coverage (Application Layer)
        uses: codecov/codecov-action@v3
        with:
          directory: ./packages/application/coverage
          flags: application
          name: application-coverage