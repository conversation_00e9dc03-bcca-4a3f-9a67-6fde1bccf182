name: CI - Domain Layer

on:
  pull_request:
    branches: [develop, main]
    paths:
      - 'packages/domain/**'
      - 'pnpm-lock.yaml'
      - '.github/workflows/**'
  push:
    branches: [develop, main]
    paths:
      - 'packages/domain/**'
      - 'pnpm-lock.yaml'
      - '.github/workflows/**'

jobs:
  domain:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js (LTS 20.x)
        uses: actions/setup-node@v4
        with:
          node-version: 20.x

      - name: Setup pnpm & install (domain only)
        uses: pnpm/action-setup@v2
        with:
          version: 8
          run_install: true
          args: >-
            --frozen-lockfile
            --filter ./packages/domain...

      - name: Lint (Domain Layer)
        run: pnpm --filter ./packages/domain lint

      - name: Test (Domain Layer)
        run: pnpm --filter ./packages/domain test

      - name: Build (Domain Layer)
        run: pnpm --filter ./packages/domain build

      - name: Upload coverage (Domain Layer)
        uses: codecov/codecov-action@v3
        with:
          directory: ./packages/domain/coverage
          flags: domain
          name: domain-coverage
